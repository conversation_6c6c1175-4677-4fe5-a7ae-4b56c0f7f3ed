import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/pages/remain_payment_summary_page/models/remain_payment_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/pages/remain_payment_summary_page/provider/remain_payment_summary_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/widgets/payment_summary_row.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

/// Payment page
class RemainPaymentScreen extends StatefulWidget {
  /// Constructor
  const RemainPaymentScreen({super.key, required this.remainPaymentParams});

  final RemainPaymentParams remainPaymentParams;

  @override
  State<RemainPaymentScreen> createState() => _RemainPaymentScreenState();
}

class _RemainPaymentScreenState extends State<RemainPaymentScreen> {
  int totalAmount = 0;
  int netTotalAmount = 0;

  @override
  void initState() {
    final paymentData =
        widget.remainPaymentParams.tripDetailProvider.paymentData.value;
    totalAmount = paymentData?.totalBookingCost?.toInt() ?? 0;
    netTotalAmount = paymentData?.totalTransportationCharge?.toInt() ?? 0;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final paymentData =
        widget.remainPaymentParams.tripDetailProvider.paymentData.value;
    return ChangeNotifierProvider(
      create: (context) => RemainPaymentSummaryProvider(),
      builder: (context, child) {
        final remainPaymentProvider = context
            .read<RemainPaymentSummaryProvider>();
        return Scaffold(
          appBar: CustomAppBar(title: context.l10n.remainPayments),
          backgroundColor: AppColors.ffF8F9FA,
          bottomNavigationBar: AppButton(
            onPressed: () => remainPaymentProvider.navigateToWebviewForPayment(
              context,
              isExclusiveTrip: widget.remainPaymentParams.isExclusiveTrip,
              // bookingId:
              //     widget
              //         .remainPaymentParams
              //         .tripDetailProvider
              //         .paymentData
              //         .value
              //         ?.transporter
              //         ?.first
              //         .id
              //         ?.toString() ??
              //     '',
              bookingId: widget.remainPaymentParams.bookingId,
            ),
            text: '\$${paymentData?.remainingBookingAmount}',
          ),
          body: SingleChildScrollView(
            padding: EdgeInsets.symmetric(
              horizontal: AppSize.appPadding,
            ).add(EdgeInsets.only(bottom: AppSize.h16)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: AppSize.h8,
              children: [
                DecoratedBox(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(AppSize.r8),
                    color: Colors.white,
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(AppSize.h16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          context.l10n.summary,
                          style: context.textTheme.titleLarge,
                        ),
                        Gap(AppSize.h8),
                        PaymentSummaryRow(
                          title: context.l10n.transportationCost,
                          amount:
                              paymentData?.totalTransportationCharge
                                  ?.toString() ??
                              '',
                        ),
                        PaymentSummaryRow(
                          title: context.l10n.dropOffStorageFee,
                          amount:
                              paymentData?.totalStartLocationStorageCharge
                                  ?.toString() ??
                              '',
                        ),
                        PaymentSummaryRow(
                          title: context.l10n.pickupStorageFee,
                          amount:
                              paymentData?.totalEndLocationStorageCharge
                                  ?.toString() ??
                              '',
                        ),
                        PaymentSummaryRow(
                          title: context.l10n.totalInsuranceCost,
                          amount:
                              paymentData?.totalInsuranceCharge?.toString() ??
                              '',
                        ),
                        PaymentSummaryRow(
                          title: context.l10n.serviceFee,
                          amount: paymentData?.totalAppFee?.toString() ?? '',
                        ),
                        if ((paymentData
                                    ?.totalCustomerLocationToStartStopLocationServiceCharge ??
                                0) >
                            0)
                          PaymentSummaryRow(
                            title: context.l10n.dropTransportation,
                            amount:
                                paymentData
                                    ?.totalCustomerLocationToStartStopLocationServiceCharge
                                    ?.toString() ??
                                '',
                          ),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(vertical: AppSize.h8),
                  child: TotalRow(
                    lastTitle: '\$$totalAmount',
                    firstTitle: context.l10n.total,
                    isLast: true,
                  ),
                ),
                TotalRow(
                  lastTitle: '\$${paymentData?.remainingBookingAmount}',
                  firstTitle: context.l10n.remainAmount,
                  isLast: true,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
