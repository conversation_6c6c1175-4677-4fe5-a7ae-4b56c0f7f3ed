import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:readmore/readmore.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/pages/car_payment_detail_page/models/car_payment_detail_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/provider/trip_detail_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/widgets/chat_icon_widget.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/completed_page/rate_page/models/rate_page_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/widgets/vehicle_info_widget.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/no_of_vehicle_widget.dart';
import 'package:transport_match/widgets/title_info.dart';

/// DetailsCard ui
class DetailsCardWidgets extends StatefulWidget {
  /// Constructor
  const DetailsCardWidgets({
    super.key,
    this.data,
    this.tripData,
    this.addDriverChatRoomData,
    required this.isExclusive,
    required this.onRatingDone,
    required this.index,
  });
  final BookingDetail? data;
  final TripModel? tripData;
  final bool isExclusive;
  final int index;
  final Function(int rating, String? description) onRatingDone;
  final void Function(int chatRoomId, bool isActive, int index, bool isCount)?
  addDriverChatRoomData;

  @override
  State<DetailsCardWidgets> createState() => _DetailsCardWidgetsState();
}

class _DetailsCardWidgetsState extends State<DetailsCardWidgets> {
  bool isOpen = false;
  @override
  Widget build(BuildContext context) {
    final isConfirm = widget.data?.status == BookingStatusType.CONFIRMED.name;
    final isDelivered = widget.data?.status == BookingStatusType.COMPLETED.name;
    final isCompleted = widget.data?.status == BookingStatusType.COMPLETED.name;
    final isOngoing = widget.data?.status == BookingStatusType.ONGOING.name;
    final isRateDone = widget.data?.ratings?.isNotEmpty ?? false;
    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.all(Radius.circular(AppSize.r4)),
      ),
      padding: EdgeInsets.all(
        AppSize.sp16,
      ).subtract(EdgeInsets.only(top: widget.isExclusive ? 0 : AppSize.h8)),
      child: Column(
        children: [
          Consumer<TripDetailProvider>(
            builder: (context, tripDetailProvider, child) {
              final paymentData = tripDetailProvider
                  .paymentSummaryData
                  ?.transporter
                  ?.where((element) {
                    return element.bookingDetailId ==
                        widget.data?.bookingDetailId;
                  })
                  .firstOrNull;
              return Row(
                mainAxisAlignment:
                    tripDetailProvider.isPaymentSummaryLoad ||
                        paymentData == null
                    ? MainAxisAlignment.end
                    : MainAxisAlignment.start,
                children: [
                  if (!tripDetailProvider.isPaymentSummaryLoad &&
                      paymentData != null)
                    GestureDetector(
                      onTap: () => AppNavigationService.pushNamed(
                        context,
                        AppRoutes.tripsCarPaymentDetailScreen,
                        extra: CarPaymentDetailParams(
                          bookingDetail: widget.data,
                          paymentData: paymentData,
                        ),
                      ),
                      child: Text(
                        context.l10n.details,
                        style: context.textTheme.bodyLarge?.copyWith(
                          color: AppColors.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ChatIconWidget(
                    isComplete: isCompleted,
                    isExclusive: widget.isExclusive,
                    isOngoing: isOngoing,
                    tripData: widget.tripData,
                    data: widget.data,
                    addDriverChatRoomData:
                        (chatRoomId, isActive, index, isCount) {
                          widget.addDriverChatRoomData?.call(
                            chatRoomId,
                            isActive,
                            index,
                            isCount,
                          );
                        },
                  ),
                  const Spacer(),
                  if (isConfirm || isCompleted)
                    GestureDetector(
                      onTap: () {
                        if (isCompleted) {
                          AppNavigationService.pushNamed(
                            context,
                            AppRoutes.tripsRateScreen,
                            extra: RateParams(
                              tripId:
                                  widget.data?.tripData?.id.toString() ?? '',
                              providerId:
                                  widget.data?.tripData?.provider.toString() ??
                                  '',
                              bookingDetail: widget.data?.id.toString() ?? '',
                              onRatingDone: (rating, description) => setState(
                                () => widget.onRatingDone(rating, description),
                              ),
                            ),
                          );
                        } else {
                          context.showAlertDialog(
                            defaultActionText: context.l10n.yes,
                            cancelActionText: context.l10n.no,
                            onCancelActionPressed: Navigator.pop,
                            onDefaultActionPressed: Navigator.pop,
                            titleWidget: Text(
                              context.l10n.cancelTrip,
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.w500,
                                color: AppColors.ff6C757D,
                              ),
                            ),
                            contentWidget: Text(
                              context.l10n.areUSureCancel,
                              textAlign: TextAlign.center,
                              style: const TextStyle(fontSize: 15),
                            ),
                          );
                        }
                      },
                      child: Text(
                        isCompleted
                            ? isRateDone
                                  ? (widget.data?.status ?? '').upToLower
                                  : context.l10n.rateProvider
                            : context.l10n.cancelTrip,
                        style: context.textTheme.bodyLarge?.copyWith(
                          color: isCompleted
                              ? AppColors.primaryColor
                              : AppColors.red,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
          if ((isCompleted && isRateDone) || (widget.isExclusive && isOngoing))
            Gap(AppSize.h16),
          if ((widget.data?.status.isNotEmptyAndNotNull ?? false) &&
              !(isCompleted && isRateDone))
            Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: EdgeInsets.only(
                  bottom: !widget.isExclusive ? AppSize.h10 : 0,
                  top: isCompleted ? AppSize.h10 : 0,
                ),
                child: RichText(
                  text: TextSpan(
                    text: '${context.l10n.bookingStatus}: ',
                    style: context.textTheme.bodyMedium?.copyWith(
                      color: AppColors.ffADB5BD,
                    ),
                    children: [
                      TextSpan(
                        text: (widget.data?.status ?? '').upToLower,
                        style: context.textTheme.bodyMedium?.copyWith(
                          color: AppColors.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          Padding(
            padding: EdgeInsets.only(
              top: !widget.isExclusive ? 0 : AppSize.h16,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TitleInfo(
                        title: context.l10n.transporter,
                        subTitle: widget.data?.tripData?.companyName ?? '',
                      ),
                      NoOfVehicleWidget(
                        noOfVehicle:
                            widget.data?.carDetails?.length.toString() ?? '0',
                      ),
                    ],
                  ),
                ),
                Flexible(
                  child: TitleInfo(
                    title: context.l10n.total_trip_cost,
                    subTitle: (widget.data?.netTripCharge.toString() ?? '')
                        .smartFormat(),
                    subTitleColor: AppColors.ff67509C,
                    subTitleFontWeight: FontWeight.w600,
                    subTitleSize: AppSize.h16,
                    isAxisEnd: true,
                  ),
                ),
              ],
            ),
          ),
          if ((widget.data?.isRemainingPaymentButtonEnabled ?? false) &&
              !isDelivered)
            Padding(
              padding: EdgeInsets.only(top: AppSize.h16),
              child: Center(
                child: AppButton(
                  text: context.l10n.payRemainAmount,
                  isFillButton: false,
                  borderColor: AppColors.primaryColor,
                  textStyle: context.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.primaryColor,
                    fontSize: AppSize.sp16,
                  ),
                  onPressed: () =>
                      context.read<TripDetailProvider>().payRemainingAmount(
                        widget.tripData?.id?.toString() ?? '',
                        context,
                        widget.data?.id?.toString() ?? '',
                        context.read<TripDetailProvider>(),
                        isExclusive: widget.isExclusive,
                      ),
                ),

                //  GestureDetector(
                //   onTap: () =>
                //       // context.read<TripDetailProvider>().payRemainingAmount(
                //       //   widget.data?.id.toString() ?? '',
                //       //   context,
                //       //   context.read<TripDetailProvider>(),
                //       //   isExclusive: widget.isExclusive,
                //       // ),
                //       context
                //           .read<TripDetailProvider>()
                //           .navigateToWebviewForPayment(
                //             context,
                //             bookingId: widget.data?.id.toString() ?? '',
                //             isExclusiveTrip: widget.isExclusive,
                //           ),
                //   child: Text(
                //     context.l10n.payRemainAmount,
                //     style: context.textTheme.bodyLarge?.copyWith(
                //       color: AppColors.ff0087C7,
                //       fontSize: AppSize.sp16,
                //       fontWeight: FontWeight.w600,
                //     ),
                //   ),
                // ),
              ),
            ),
          if (widget.data?.ratings?.isNotEmpty ?? false)
            Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: EdgeInsets.only(top: AppSize.h10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${context.l10n.myRating} (${widget.data?.ratings?.firstOrNull?.rating}★)',
                      style: context.textTheme.titleMedium?.copyWith(
                        fontSize: AppSize.sp16,
                      ),
                    ),
                    if (widget
                            .data
                            ?.ratings
                            ?.firstOrNull
                            ?.suggestion
                            ?.isNotEmpty ??
                        false)
                      ReadMoreText(
                        widget.data?.ratings?.firstOrNull?.suggestion ?? '',
                        style: context.textTheme.titleMedium?.copyWith(
                          fontSize: AppSize.sp14,
                          color: AppColors.ffADB5BD,
                        ),
                        trimLength: 50,
                        trimCollapsedText: context.l10n.readMore,
                        trimExpandedText: ' ${context.l10n.readLess}',
                        lessStyle: context.textTheme.titleMedium?.copyWith(
                          fontSize: AppSize.sp14,
                          color: AppColors.ff0087C7,
                        ),
                        moreStyle: context.textTheme.titleMedium?.copyWith(
                          fontSize: AppSize.sp14,
                          color: AppColors.ff0087C7,
                        ),
                      ),
                  ],
                ),
              ),
            ),
          if (isOpen)
            Padding(
              padding: EdgeInsets.only(top: AppSize.h16),
              child: VehiclesInfoWidget(
                isExclusiveScreen: false,
                carList: (widget.data?.carDetails ?? [])
                    .map((e) => VehicleInfoModel.fromJson(e.toJson()))
                    .toList(),
              ),
            ),
          Padding(
            padding: EdgeInsets.only(top: AppSize.h16),
            child: GestureDetector(
              onTap: () => setState(() => isOpen = !isOpen),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                spacing: AppSize.w4,
                children: [
                  Icon(
                    isOpen ? Icons.close : Icons.add,
                    color: AppColors.primaryColor,
                    size: AppSize.sp24,
                  ),
                  Text(
                    isOpen ? context.l10n.close : context.l10n.viewDetails,
                    style: context.textTheme.bodyLarge?.copyWith(
                      color: AppColors.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
