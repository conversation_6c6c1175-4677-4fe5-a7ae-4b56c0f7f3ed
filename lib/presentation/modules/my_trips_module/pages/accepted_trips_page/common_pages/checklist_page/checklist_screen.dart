import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/checklist_page/models/checklist_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/checklist_page/provider/checklist_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/models/checklist_model.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

///Check list Screen Ui
class ChecklistScreen extends StatelessWidget {
  /// Constructor
  const ChecklistScreen({super.key, required this.checklistParams});
  final ChecklistParams checklistParams;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => ChecklistProvider()
        ..getCheckList(
          checklistParams.carId,
          context,
          clientName: checklistParams.clientName,
          isWantShowLoader: true,
        ),
      child: Builder(
        builder: (context) {
          final checkListProvider = context.read<ChecklistProvider>();
          return Scaffold(
            backgroundColor: AppColors.ffF8F9FA,
            appBar: CustomAppBar(title: context.l10n.checklist),
            body: Selector<ChecklistProvider, (bool, String?)>(
              selector: (p0, checklistProvider) => (
                checklistProvider.isCheckListShowLoader,
                checklistProvider.checkListNextUrl,
              ),
              builder: (context, isCheckListShowLoader, child) {
                return AppLoader(
                  isShowLoader: isCheckListShowLoader.$1,
                  child: SizedBox.expand(
                    child: EasyRefresh(
                      triggerAxis: Axis.vertical,
                      header: AppCommonFunctions.getLoadingHeader(),
                      footer: AppCommonFunctions.getLoadingFooter(),
                      controller: checkListProvider.refreshController,
                      onRefresh: () async => checkListProvider.getCheckList(
                        checklistParams.carId,
                        context,
                        clientName: checklistParams.clientName,
                      ),
                      onLoad: () async =>
                          isCheckListShowLoader.$2.isNotEmptyAndNotNull
                          ? checkListProvider.getCheckList(
                              checklistParams.carId,
                              context,
                              clientName: checklistParams.clientName,
                              isPagination: true,
                            )
                          : null,
                      child:
                          !isCheckListShowLoader.$1 &&
                              checkListProvider.checkList.isEmpty
                          ? ListView(
                            children: [
                              SizedBox(
                                height: MediaQuery.of(context).size.height * 0.8,
                                child: Center(
                                    child: Padding(
                                      padding: EdgeInsets.only(
                                        bottom: AppSize.h40,
                                        right: AppSize.appPadding,
                                        left: AppSize.appPadding,
                                      ),
                                      child: Text(
                                        context.l10n.noCheckListFound,
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ),
                              ),
                            ],
                          )
                          : child!,
                    ),
                  ),
                );
              },
              child: Selector<ChecklistProvider, List<ChecklistModel>>(
                selector: (p0, checklistProvider) =>
                    checklistProvider.checkList,
                builder: (context, checklist, child) {
                  return ListView.builder(
                    itemCount: checklist.length,
                    padding: EdgeInsets.symmetric(
                      horizontal: AppSize.appPadding,
                      vertical: AppSize.h10,
                    ),
                    primary: false,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      final checkList = checkListProvider.checkList[index];

                      final Widget child = Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        spacing: 5,
                        children: [
                          richText(
                            title: context.l10n.type,
                            value: checkList.checklistType?.upToLower ?? '',
                          ),
                          richText(
                            title: context.l10n.status,
                            value:checkList.checklistType == 'DRIVER' && checkList.performedDuring == 'COLLECTING_FROM_STOP_ADMIN'?
                                checkList.stopAdminChecklistVerificationStatus?.upToLower ?? ''
                                :
                                checkList.checklistType == 'END_STOP_ADMIN' && checkList.performedDuring == 'COLLECTING_FROM_DRIVER'?
                                checkList.driverChecklistVerificationStatus?.upToLower ?? ''
                                :
                                checkList.checklistType == 'END_STOP_ADMIN' && checkList.performedDuring == 'HANDED_OVER_TO_CUSTOMER'?
                                checkList.customerChecklistVerificationStatus?.upToLower ??''
                                : checkList.customerChecklistVerificationStatus?.upToLower ??''

                          ),
                          richText(
                            title: context.l10n.performedDuring,
                            value: checkList.performedDuring?.upToLower ?? '',
                          ),
                          Align(
                            alignment: Alignment.centerRight,
                            child: Text(
                              checkList.createdAt?.mmmDdYyyy ?? '',
                              style: TextStyle(
                                fontSize: AppSize.sp11,
                                fontWeight: FontWeight.w600,
                                color: const Color.fromARGB(255, 45, 47, 48),
                              ),
                            ),
                          ),
                        ],
                      );

                      return Padding(
                        padding: EdgeInsets.only(bottom: AppSize.h16),
                        child: GestureDetector(
                          onTap: () => checkListProvider.assignCheckListData(
                            context,
                            clientName: checklistParams.clientName,
                            data: checkList,
                            checkListProvider: checkListProvider,
                          ),
                          child:
                              checklist[index].id?.toString() ==
                                  checklistParams.checkListId
                              ? _BlinkWidget(child, (context) {
                                  /// auto scroll to this widget if it is not visible
                                  Scrollable.ensureVisible(
                                    context,
                                    curve: Curves.decelerate,
                                    duration: const Duration(milliseconds: 800),
                                  );
                                })
                              : DecoratedBox(
                                  decoration: BoxDecoration(
                                    color: AppColors.white,
                                    borderRadius: BorderRadius.circular(
                                      AppSize.r8,
                                    ),
                                    border: Border.all(
                                      color: AppColors.ffADB5BD,
                                    ),
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.all(AppSize.sp16),
                                    child: child,
                                  ),
                                ),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }

  Widget richText({required String title, required String value}) {
    return RichText(
      text: TextSpan(
        text: '$title: ',
        style: TextStyle(
          fontSize: AppSize.sp14,
          color: AppColors.ffADB5BD,
          fontWeight: FontWeight.w500,
        ),
        children: [
          TextSpan(
            text: value,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.black,
            ),
          ),
        ],
      ),
    );
  }
}

class _BlinkWidget extends StatefulWidget {
  const _BlinkWidget(this.child, this.onInit);
  final Widget child;
  final Function(BuildContext context) onInit;

  @override
  State<_BlinkWidget> createState() => __BlinkWidgetState();
}

class __BlinkWidgetState extends State<_BlinkWidget>
    with TickerProviderStateMixin {
  late Animation<Color?> animation;
  late Animation<double?> widthAnimation;
  late AnimationController controller;
  int count = 0;

  @override
  void initState() {
    super.initState();
    controller = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    final curve = CurvedAnimation(parent: controller, curve: Curves.linear);
    animation = ColorTween(
      begin: AppColors.ffADB5BD,
      end: Colors.red,
    ).animate(curve);
    widthAnimation = Tween<double>(begin: 1, end: 2).animate(curve);
    animation.addStatusListener((status) {
      if (count < 11) {
        count++;
        if (status == AnimationStatus.completed) {
          controller.reverse();
        } else if (status == AnimationStatus.dismissed) {
          controller.forward();
        }
      } else {
        controller.stop();
      }
      setState(() {});
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.onInit(context);
    });
    controller.forward();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        return DecoratedBox(
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(AppSize.r8),
            border: Border.all(
              color: animation.value ?? AppColors.ffADB5BD,
              width: widthAnimation.value ?? 1,
              //  ??
              //     AppColors.ffADB5BD,
            ),
          ),
          child: child,
        );
      },
      child: Padding(
        padding: EdgeInsets.all(AppSize.sp16),
        child: widget.child,
      ),
    );
  }

  // double controllerVal() {
  //   print('==>>> ${_controller.value}');
  //   return switch (_controller.value) {
  //     >= 2 || < 4 => 1 - (_controller.value - 3).abs(),
  //     >= 4 || < 6 => 1 - (_controller.value - 5).abs(),
  //     _ => 1 - (_controller.value - 7).abs(),
  //   };
  // }
}
