import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/checklist_page/provider/checklist_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/models/checklist_model.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/logger.dart';
import 'package:transport_match/widgets/app_image.dart';
import 'package:transport_match/widgets/widget_zoom/widget_zoom.dart';

class ChecklistImageWidget extends StatelessWidget {
  const ChecklistImageWidget({super.key, this.checklistModel});
  final ChecklistModel? checklistModel;

  @override
  Widget build(BuildContext context) {
    return Consumer<ChecklistProvider>(
      builder: (context, checklistProvider, child) {
        final isAdd = checklistModel == null;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 15, bottom: 10),
              child: Text(
                context.l10n.uploadCarImg,
                style: context.textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ...checklistProvider.angleList.map((e) {
              final imageList = (checklistModel?.images ?? [])
                  .where((image) => image.angle?.toLowerCase() == e)
                  .toList();
              return imageList.isEmpty && !isAdd
                  ? const SizedBox()
                  : Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'From $e side',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        SizedBox(
                          height: 95,
                          child: ListView.builder(
                            shrinkWrap: true,
                            padding: const EdgeInsets.only(top: 5, bottom: 10),
                            scrollDirection: Axis.horizontal,
                            itemCount: imageList.length,
                            itemBuilder: (context, index) {
                              return Padding(
                                padding: const EdgeInsets.only(right: 10),
                                child: Builder(
                                  builder: (context) {
                                    final img = imageList[index].imageUrl ?? '';
                                    return Stack(
                                      alignment: AlignmentDirectional.topEnd,
                                      children: [
                                        ClipRRect(
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                          child: ColoredBox(
                                            color: AppColors.ffF2EEF8,
                                            child: WidgetZoom(
                                              heroAnimationTag: img,
                                              zoomWidget: AppImage.network(
                                                img,
                                                width: 80,
                                                height: 80,
                                                fit: BoxFit.contain,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    );
                                  },
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    );
            }),
          ],
        );
      },
    );
  }
}
