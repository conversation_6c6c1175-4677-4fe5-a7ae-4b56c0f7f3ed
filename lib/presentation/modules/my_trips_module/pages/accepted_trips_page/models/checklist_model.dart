// ignore_for_file: library_private_types_in_public_api

import 'package:transport_match/extensions/ext_string_null.dart';

class CheckListData {
  CheckListData({
    this.count,
    this.next,
    this.previous,
    this.results = const [],
  });

  factory CheckListData.fromJson(Map<String, dynamic> json) => CheckListData(
        count: json['count'] as int?,
        next: json['next'] as String?,
        previous: json['previous'] as String?,
        results: json['results'] == null
            ? []
            : List<ChecklistModel>.from(
                (json['results'] as List<dynamic>).map(
                  (x) => ChecklistModel.fromJson(x as Map<String, dynamic>),
                ),
              ),
      );
  int? count;
  String? next;
  String? previous;
  List<ChecklistModel> results;

  Map<String, dynamic> toJson() => {
        'count': count,
        'next': next,
        'previous': previous,
        'results': List<dynamic>.from(results.map((x) => x.toJson())),
      };
}

class ChecklistModel {
  ChecklistModel({
    this.id,
    this.user,
    this.checklistType,
    this.bookedCar,
    this.performedDuring,
    this.mileage,
    this.fuelLevel,
    this.other,
    this.mainLights = false,
    this.mediumLights = false,
    this.stopLightOrTurnSignals = false,
    this.radioAntenna = false,
    this.pairOfWindShieldsWipers = false,
    this.rightSideMirror = false,
    this.sideWindows = false,
    this.windshield = false,
    this.rearWindow = false,
    this.fourWheelCaps = false,
    this.bodyWithoutDents = false,
    this.frontBumper = false,
    this.rearBumper = false,
    this.frontLicensePlate = false,
    this.rearLicensePlate = false,
    this.heating = false,
    this.radio = false,
    this.speakers = false,
    this.lighter = false,
    this.rearviewMirror = false,
    this.ashtrays = false,
    this.seatBelts = false,
    this.windowHandles = false,
    this.rubberFloors = false,
    this.floorMats = false,
    this.seatCovers = false,
    this.doorHandles = false,
    this.holder = false,
    this.engine = false,
    this.jack = false,
    this.wheelWrench = false,
    this.toolKit = false,
    this.triangle = false,
    this.spareTire = false,
    this.fireExtinguisher = false,
    this.scratchedPaint = false,
    this.brokenWindows = false,
    this.dents = false,
    this.suspension = false,
    this.images,
    this.createdAt,
    this.awsImageKeys,
    this.carId,
    this.commentList,
    this.customerChecklistVerificationStatus,
    this.stopAdminChecklistVerificationStatus,
    this.driverChecklistVerificationStatus
  });

  factory ChecklistModel.fromJson(Map<String, dynamic> json) => ChecklistModel(
        id: json['id'] as int?,
        user: json['user'] == null
            ? null
            : UserDataModel.fromJson(json['user'] as Map<String, dynamic>),
        checklistType: json['checklist_type'] as String?,
        bookedCar: json['booked_car'] == null
            ? null
            : BookedCarModel.fromJson(
                json['booked_car'] as Map<String, dynamic>,
              ),
        performedDuring: json['performed_during'] as String?,
        mileage: json['mileage'] as num?,
        fuelLevel: json['fuel_level'] as String?,
        customerChecklistVerificationStatus:
            json['customer_checklist_verification_status'] as String?,
        stopAdminChecklistVerificationStatus: json['start_stop_admin_checklist_verification_status'] as String?,
        driverChecklistVerificationStatus: json['driver_checklist_verification_status'] as String?,
        other: json['other'] as String?,
        mainLights: (json['main_lights'] as bool?) ?? false,
        mediumLights: (json['medium_lights'] as bool?) ?? false,
        stopLightOrTurnSignals:
            (json['stop_light_or_turn_signals'] as bool?) ?? false,
        radioAntenna: (json['radio_antenna'] as bool?) ?? false,
        pairOfWindShieldsWipers:
            (json['pair_of_wind_shields_wipers'] as bool?) ?? false,
        rightSideMirror: (json['right_side_mirror'] as bool?) ?? false,
        sideWindows: (json['side_windows'] as bool?) ?? false,
        windshield: (json['windshield'] as bool?) ?? false,
        rearWindow: (json['rear_window'] as bool?) ?? false,
        fourWheelCaps: (json['four_wheel_caps'] as bool?) ?? false,
        bodyWithoutDents: (json['body_without_dents'] as bool?) ?? false,
        frontBumper: (json['front_bumper'] as bool?) ?? false,
        rearBumper: (json['rear_bumper'] as bool?) ?? false,
        frontLicensePlate: (json['front_license_plate'] as bool?) ?? false,
        rearLicensePlate: (json['rear_license_plate'] as bool?) ?? false,
        heating: (json['heating'] as bool?) ?? false,
        radio: (json['radio'] as bool?) ?? false,
        speakers: (json['speakers'] as bool?) ?? false,
        lighter: (json['lighter'] as bool?) ?? false,
        rearviewMirror: (json['rearview_mirror'] as bool?) ?? false,
        ashtrays: (json['ashtrays'] as bool?) ?? false,
        seatBelts: (json['seat_belts'] as bool?) ?? false,
        windowHandles: (json['window_handles'] as bool?) ?? false,
        rubberFloors: (json['rubber_floors'] as bool?) ?? false,
        floorMats: (json['floor_mats'] as bool?) ?? false,
        seatCovers: (json['seat_covers'] as bool?) ?? false,
        doorHandles: (json['door_handles'] as bool?) ?? false,
        holder: (json['holder'] as bool?) ?? false,
        engine: (json['engine'] as bool?) ?? false,
        jack: (json['jack'] as bool?) ?? false,
        wheelWrench: (json['wheel_wrench'] as bool?) ?? false,
        toolKit: (json['tool_kit'] as bool?) ?? false,
        triangle: (json['triangle'] as bool?) ?? false,
        spareTire: (json['spare_tire'] as bool?) ?? false,
        fireExtinguisher: (json['fire_extinguisher'] as bool?) ?? false,
        scratchedPaint: (json['scratched_paint'] as bool?) ?? false,
        brokenWindows: (json['broken_windows'] as bool?) ?? false,
        dents: (json['dents'] as bool?) ?? false,
        suspension: (json['suspension'] as bool?) ?? false,
        images: json['images'] == null
            ? []
            : List<ImageModel>.from(
                (json['images'] as List?)?.map(
                      (x) => ImageModel.fromJson(x as Map<String, dynamic>),
                    ) ??
                    [],
              ),
        createdAt: json['created_at'] == null
            ? null
            : DateTime.parse(json['created_at'] as String),
        commentList: json['comments'] == null
            ? []
            : List<CommentsModel>.from(
                (json['comments'] as List?)?.map(
                      (x) => CommentsModel.fromJson(x as Map<String, dynamic>),
                    ) ??
                    [],
              ),
      );
  int? id;
  UserDataModel? user;
  String? checklistType;
  BookedCarModel? bookedCar;
  String? performedDuring;
  num? mileage;
  String? fuelLevel;
  String? carId;
  String? other;
  bool mainLights;
  bool mediumLights;
  bool stopLightOrTurnSignals;
  bool radioAntenna;
  bool pairOfWindShieldsWipers;
  bool rightSideMirror;
  bool sideWindows;
  bool windshield;
  bool rearWindow;
  bool fourWheelCaps;
  bool bodyWithoutDents;
  bool frontBumper;
  bool rearBumper;
  bool frontLicensePlate;
  bool rearLicensePlate;
  bool heating;
  bool radio;
  bool speakers;
  bool lighter;
  bool rearviewMirror;
  bool ashtrays;
  bool seatBelts;
  bool windowHandles;
  bool rubberFloors;
  bool floorMats;
  bool seatCovers;
  bool doorHandles;
  bool holder;
  bool engine;
  bool jack;
  bool wheelWrench;
  bool toolKit;
  bool triangle;
  bool spareTire;
  bool fireExtinguisher;
  bool scratchedPaint;
  bool brokenWindows;
  bool dents;
  bool suspension;
  String? customerChecklistVerificationStatus;
  String? stopAdminChecklistVerificationStatus;
  String? driverChecklistVerificationStatus;
  List<ImageModel>? images;
  DateTime? createdAt;
  List<CommentsModel>? commentList;
  Map<String, dynamic>? awsImageKeys;

  Map<String, dynamic> toJson() => {
        if (id != null) 'id': id,
        if (user != null) 'user': user?.toJson(),
        if (checklistType != null) 'checklist_type': checklistType,
        'booked_car': bookedCar?.toJson() ?? carId,
        'performed_during': performedDuring,
        'mileage': mileage,
        'fuel_level': fuelLevel,
        'other': other,
        'main_lights': mainLights,
        'medium_lights': mediumLights,
        'stop_light_or_turn_signals': stopLightOrTurnSignals,
        'radio_antenna': radioAntenna,
        'pair_of_wind_shields_wipers': pairOfWindShieldsWipers,
        'right_side_mirror': rightSideMirror,
        'side_windows': sideWindows,
        'windshield': windshield,
        'rear_window': rearWindow,
        'four_wheel_caps': fourWheelCaps,
        'body_without_dents': bodyWithoutDents,
        'front_bumper': frontBumper,
        'rear_bumper': rearBumper,
        'front_license_plate': frontLicensePlate,
        'rear_license_plate': rearLicensePlate,
        'heating': heating,
        'radio': radio,
        'speakers': speakers,
        'lighter': lighter,
        'rearview_mirror': rearviewMirror,
        'ashtrays': ashtrays,
        'seat_belts': seatBelts,
        'window_handles': windowHandles,
        'rubber_floors': rubberFloors,
        'floor_mats': floorMats,
        'seat_covers': seatCovers,
        'door_handles': doorHandles,
        'holder': holder,
        'engine': engine,
        'jack': jack,
        'wheel_wrench': wheelWrench,
        'tool_kit': toolKit,
        'triangle': triangle,
        'spare_tire': spareTire,
        'fire_extinguisher': fireExtinguisher,
        if (customerChecklistVerificationStatus.isNotEmptyAndNotNull)
          'customer_checklist_verification_status':
              customerChecklistVerificationStatus,
        'scratched_paint': scratchedPaint,
        'broken_windows': brokenWindows,
        'dents': dents,
        'suspension': suspension,
        'aws_image_keys': awsImageKeys,
        if (images != null)
          'images': images == null
              ? []
              : List<dynamic>.from(images!.map((x) => x.toJson())),
        if (commentList != null)
          'comments': commentList == null
              ? []
              : List<dynamic>.from(commentList!.map((x) => x.toJson())),
        if (createdAt != null)
          'created_at': createdAt?.toUtc().toIso8601String(),
      };
}

class CommentsModel {
  CommentsModel({
    this.id,
    this.description,
    this.firstName,
    this.createdAt,
  });

  factory CommentsModel.fromJson(Map<String, dynamic> json) => CommentsModel(
        id: json['id'] as int?,
        description: json['description'] as String?,
        firstName: json['first_name'] as String?,
        createdAt: json['created_at'] == null
            ? null
            : DateTime.parse(json['created_at'] as String),
      );
  int? id;
  String? description;
  String? firstName;
  DateTime? createdAt;

  Map<String, dynamic> toJson() => {
        'id': id,
        'first_name': firstName,
        'description': description,
        'created_at': createdAt,
      };
}

class BookedCarModel {
  BookedCarModel({
    this.id,
    this.serialNumber,
    this.requiredWinch = false,
    this.isPairable = false,
    this.isPaired = false,
    this.netCarCharge = 0,
    this.carDescription,
    this.status,
    this.refundStatus,
    this.isPairedChargeSettled = false,
    this.bookedCarId,
    this.car,
  });

  factory BookedCarModel.fromJson(Map<String, dynamic> json) => BookedCarModel(
        id: json['id'] as int?,
        serialNumber: json['serial_number'] as String?,
        requiredWinch: (json['required_winch'] as bool?) ?? false,
        isPairable: (json['is_pairable'] as bool?) ?? false,
        isPaired: (json['is_paired'] as bool?) ?? false,
        netCarCharge: json['net_car_charge'] as num?,
        carDescription: json['car_description'] as String?,
        status: json['status'] as String?,
        refundStatus: json['refund_status'] as String?,
        isPairedChargeSettled:
            (json['is_paired_charge_settled'] as bool?) ?? false,
        bookedCarId: json['booked_car_id'] as String?,
        car: json['car'] == null
            ? null
            : _CarModel.fromJson(json['car'] as Map<String, dynamic>),
      );
  int? id;
  String? serialNumber;
  bool requiredWinch;
  bool isPairable;
  bool isPaired;
  num? netCarCharge;
  String? carDescription;
  String? status;
  String? refundStatus;
  bool isPairedChargeSettled;
  String? bookedCarId;
  _CarModel? car;

  Map<String, dynamic> toJson() => {
        'id': id,
        'serial_number': serialNumber,
        'required_winch': requiredWinch,
        'is_pairable': isPairable,
        'is_paired': isPaired,
        'net_car_charge': netCarCharge,
        'car_description': carDescription,
        'status': status,
        'refund_status': refundStatus,
        'is_paired_charge_settled': isPairedChargeSettled,
        'booked_car_id': bookedCarId,
        'car': car?.toJson(),
      };
}

class _CarModel {
  _CarModel({
    this.id,
    this.size,
    this.model,
    this.year,
    this.brand,
    this.createdAt,
    this.isDeleted = false,
    this.deletedAt,
  });

  factory _CarModel.fromJson(Map<String, dynamic> json) => _CarModel(
        id: json['id'] as int?,
        size: json['size'] as num?,
        model: json['model'] as String?,
        year: json['year'] as String?,
        brand: json['brand'] as String?,
        createdAt: json['created_at'] == null
            ? null
            : DateTime.parse(json['created_at'] as String),
        isDeleted: (json['is_deleted'] as bool?) ?? false,
        deletedAt: json['deleted_at'] == null
            ? null
            : DateTime.parse(json['deleted_at'] as String),
      );
  int? id;
  num? size;
  String? model;
  String? year;
  String? brand;
  DateTime? createdAt;
  bool isDeleted;
  DateTime? deletedAt;

  Map<String, dynamic> toJson() => {
        'id': id,
        'size': size,
        'model': model,
        'year': year,
        'brand': brand,
        'created_at': createdAt?.toUtc().toIso8601String(),
        'is_deleted': isDeleted,
        'deleted_at': deletedAt?.toUtc().toIso8601String(),
      };
}

class ImageModel {
  ImageModel({
    this.id,
    this.angle,
    this.imageUrl,
  });

  factory ImageModel.fromJson(Map<String, dynamic> json) => ImageModel(
        id: json['id'] as int?,
        angle: json['angle'] as String?,
        imageUrl: json['image_url'] as String?,
      );
  int? id;
  String? angle;
  String? imageUrl;

  Map<String, dynamic> toJson() => {
        'id': id,
        'angle': angle,
        'image_url': imageUrl,
      };
}

class UserDataModel {
  UserDataModel({
    this.firstName,
    this.lastName,
    this.email,
  });

  factory UserDataModel.fromJson(Map<String, dynamic> json) => UserDataModel(
        firstName: json['first_name'] as String?,
        lastName: json['last_name'] as String?,
        email: json['email'] as String?,
      );
  String? firstName;
  String? lastName;
  String? email;

  Map<String, dynamic> toJson() => {
        'first_name': firstName,
        'last_name': lastName,
        'email': email,
      };
}
