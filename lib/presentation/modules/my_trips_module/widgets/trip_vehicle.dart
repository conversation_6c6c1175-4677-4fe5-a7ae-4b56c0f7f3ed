import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/common_pages/car_info_page/car_info_screen.dart';
import 'package:transport_match/presentation/modules/booking_module/provider/booking_provider.dart';
import 'package:transport_match/presentation/modules/home_module/models/provider_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/exclusive_trip_screen/models/insurance_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_confirm_check_box.dart';
import 'package:transport_match/widgets/app_dropdown.dart';
import 'package:transport_match/widgets/keep_alive_wrapper.dart';
import 'package:transport_match/widgets/transport_details_card_widget.dart';
import 'package:transport_match/widgets/vehicle_image_selection_widget.dart';

/// Stock screen vehicle information
class TripVehiclesInfoWidget extends StatefulWidget {
  /// Constructor
  const TripVehiclesInfoWidget({
    super.key,
    this.onClose,
    required this.selectedTripData,
    required this.bookingProvider,
    this.isTrip = false,
    required this.mainIndex,
    this.providerListData,
  });

  final VoidCallback? onClose;
  final TripModel? selectedTripData;
  final BookingProvider bookingProvider;
  final bool isTrip;
  final int mainIndex;
  final ProviderListData? providerListData;

  @override
  State<TripVehiclesInfoWidget> createState() => _TripVehiclesInfoWidgetState();
}

class _TripVehiclesInfoWidgetState extends State<TripVehiclesInfoWidget> {
  late PageController _pageController;
  bool isOpen = true;
  final controllerList = <TextEditingController>[];
  final insuranceDropdownControllers = <SingleValueDropDownController>[];

  @override
  void initState() {
    _pageController = PageController();
    for (
      var i = 0;
      i < (widget.selectedTripData?.carDetails?.length ?? 0);
      i++
    ) {
      controllerList.add(TextEditingController());
      insuranceDropdownControllers.add(SingleValueDropDownController());
    }

    super.initState();
  }

  @override
  void dispose() {
    _pageController.dispose();
    for (final controller in controllerList) {
      controller.dispose();
    }
    for (final controller in insuranceDropdownControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isTrip = widget.isTrip;
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(AppSize.r10)),
        color: AppColors.white,
      ),
      padding: EdgeInsets.all(AppSize.h12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          TransportDetailsCard(
            isTrip: isTrip,
            isFirstScreen: false,
            totalCar: widget.selectedTripData?.carDetails?.length ?? 0,
            selectedTripData: widget.selectedTripData,
            providerData: widget.providerListData,
            isOpen: isOpen,
            onOpen: () {
              setState(() {
                isOpen = !isOpen;
              });

              /// this future delay set state is to listen is insurance variable
              /// coz first set state render below object and then seconde after rendered
              /// widget it'll update is insurance variable
              Future.delayed(Durations.short1, () => setState(() {}));
            },
          ),
          if (isOpen) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  context.l10n.vehicleInfo,
                  style: context.textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                  ),
                ),
                AnimatedBuilder(
                  animation: _pageController,
                  builder: (context, child) {
                    return Text(
                      '${(_pageController.positions.isEmpty ? 0 : _pageController.page?.round() ?? 0) + 1}'
                      '/${widget.bookingProvider.transporterDetailList.value[widget.mainIndex].carDetails.length}',
                      style: context.textTheme.bodyLarge?.copyWith(
                        fontSize: AppSize.sp16,
                        fontWeight: FontWeight.w400,
                      ),
                    );
                  },
                ),
              ],
            ),
            Gap(AppSize.h8),
            SizedBox(
              height: AppSize.h400 + AppSize.h8,
              child: PageView.builder(
                controller: _pageController,
                itemCount: widget
                    .bookingProvider
                    .transporterDetailList
                    .value[widget.mainIndex]
                    .carDetails
                    .length,
                itemBuilder: (context, carIndex) {
                  final data = widget
                      .bookingProvider
                      .transporterDetailList
                      .value[widget.mainIndex]
                      .carDetails[carIndex];
                  return KeepAliveWrapper(
                    child: Container(
                      padding: EdgeInsets.all(AppSize.h12),
                      margin: EdgeInsets.symmetric(horizontal: AppSize.w4),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.ffADB5BD),
                        borderRadius: BorderRadius.circular(AppSize.r5),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        spacing: AppSize.h8,
                        children: [
                          if (data.brand != null && data.carName != null)
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                if (data.brand != null)
                                  VehiclesInfoField(
                                    title: context.l10n.carBrand,
                                    value: data.brand ?? '',
                                  ),
                                if (data.carName != null)
                                  VehiclesInfoField(
                                    title: context.l10n.carModel,
                                    value: data.carName ?? '',
                                    // isLast: true,
                                  ),
                              ],
                            ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              if (data.serialNumber != null)
                                VehiclesInfoField(
                                  title: context.l10n.carSerial,
                                  value: data.serialNumber ?? '',
                                ),
                              if (data.year != null)
                                VehiclesInfoField(
                                  title: context.l10n.carYear,
                                  value: data.year?.toString() ?? '-',
                                  // isLast: true,
                                ),
                            ],
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              if (data.carSize != null)
                                VehiclesInfoField(
                                  title: context.l10n.carSize,
                                  value: data.carSize?.toString() ?? '-',
                                )
                              else if (data.carSize != null)
                                Row(
                                  spacing: AppSize.w4,
                                  children: [
                                    Text(
                                      '${context.l10n.carSize}:',
                                      style: context.textTheme.bodyLarge
                                          ?.copyWith(
                                            fontSize: AppSize.sp14,
                                            color: AppColors.ffADB5BD,
                                            fontWeight: FontWeight.w500,
                                          ),
                                    ),
                                    Text(
                                      switch (data.carSize) {
                                        1 => context.l10n.small,
                                        1.5 => context.l10n.medium,
                                        _ => context.l10n.large,
                                      },
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      style: context.textTheme.bodyLarge
                                          ?.copyWith(
                                            fontSize: AppSize.sp14,
                                            fontWeight: FontWeight.w400,
                                          ),
                                    ),
                                  ],
                                ),
                            ],
                          ),
                          ChangeNotifierProvider.value(
                            value: widget.bookingProvider,
                            child: ValueListenableBuilder(
                              valueListenable:
                                  widget.bookingProvider.insuranceList,
                              builder: (context, insuranceList, child) {
                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.only(top: AppSize.h8),
                                      child: Row(
                                        children: [
                                          AppConfirmCheckBox(
                                            onSelectionChanged: ({required value}) {
                                              widget.bookingProvider
                                                ..transporterDetailList
                                                        .value[widget.mainIndex]
                                                        .carDetails[carIndex]
                                                        .isInsuranceIncluded =
                                                    value
                                                ..notify();
                                              if (!value) {
                                                widget.bookingProvider
                                                  ..transporterDetailList
                                                          .value[widget
                                                              .mainIndex]
                                                          .carDetails[carIndex]
                                                          .insurance =
                                                      ''
                                                  ..notify();
                                                controllerList[carIndex]
                                                    .clear();
                                              } else {
                                                widget.bookingProvider
                                                    .getInsurance();
                                              }
                                              setState(() {});
                                            },
                                            value: data.isInsuranceIncluded,
                                          ),
                                          Gap(AppSize.h8),
                                          Flexible(
                                            fit: FlexFit.tight,
                                            child: Text(
                                              context.l10n.includeInsurance,
                                              style: context
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.copyWith(
                                                    fontSize: AppSize.sp14,
                                                    fontWeight: FontWeight.w400,
                                                  ),
                                            ),
                                          ),
                                          Text(
                                            '\$${insuranceList.firstWhere((element) => element.id.toString() == data.insurance, orElse: InsuranceModel.new).amount ?? 0} '
                                                .smartFormat(),
                                            style: context.textTheme.bodyMedium
                                                ?.copyWith(
                                                  color: AppColors.ff67509C,
                                                  fontSize: AppSize.sp14,
                                                  fontWeight: FontWeight.w700,
                                                ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Gap(AppSize.h10),
                                    Builder(
                                      builder: (context) {
                                        final selectedInsurance = insuranceList
                                            .where(
                                              (element) =>
                                                  element.id.toString() ==
                                                  data.insurance,
                                            )
                                            .firstOrNull;

                                        // Update controller when insurance changes
                                        WidgetsBinding.instance.addPostFrameCallback((
                                          _,
                                        ) {
                                          if (carIndex <
                                              insuranceDropdownControllers
                                                  .length) {
                                            if (selectedInsurance != null) {
                                              insuranceDropdownControllers[carIndex]
                                                  .setDropDown(
                                                    DropDownValueModel(
                                                      value: selectedInsurance
                                                          .id
                                                          ?.toString(),
                                                      name:
                                                          "${selectedInsurance.name} (\$${selectedInsurance.amount ?? ''})",
                                                    ),
                                                  );
                                            } else {
                                              insuranceDropdownControllers[carIndex]
                                                  .clearDropDown();
                                            }
                                          }
                                        });

                                        return AbsorbPointer(
                                          absorbing: !data.isInsuranceIncluded,
                                          child: AppDropdown(
                                            controller:
                                                carIndex <
                                                    insuranceDropdownControllers
                                                        .length
                                                ? insuranceDropdownControllers[carIndex]
                                                : SingleValueDropDownController(),
                                            items: insuranceList
                                                .map(
                                                  (e) => DropDownValueModel(
                                                    name:
                                                        "${e.name} (\$${(e.amount ?? '').toString().smartFormat()})",
                                                    value: e.id?.toString(),
                                                  ),
                                                )
                                                .toList(),
                                            onChanged: (p0) {
                                              if (p0 is DropDownValueModel &&
                                                  p0.value != null) {
                                                widget.bookingProvider
                                                  ..transporterDetailList
                                                          .value[widget
                                                              .mainIndex]
                                                          .carDetails[carIndex]
                                                          .insurance =
                                                      p0.value as String
                                                  ..notify();
                                                setState(() {});
                                              }
                                            },
                                            labelText:
                                                context.l10n.insuranceProvider,
                                          ),
                                        );
                                      },
                                    ),
                                    Text(
                                      '* ${context.l10n.insuranceCharges}',
                                      style: TextStyle(
                                        fontSize: AppSize.sp10,
                                        color: AppColors.red,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ],
                                );
                              },
                            ),
                          ),
                          Divider(
                            height: AppSize.h8,
                            color: AppColors.ffF2EEF8,
                          ),
                          Text(
                            '* ${context.l10n.attachPhotos}',
                            style: TextStyle(
                              fontSize: AppSize.sp10,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          Selector<BookingProvider, List<String>?>(
                            selector: (p0, p1) => p1
                                .transporterDetailList
                                .value[widget.mainIndex]
                                .carDetails[carIndex]
                                .fileImage,
                            builder: (context, fileImageList, child) {
                              return Expanded(
                                child: VehicleImageSelectionWidget(
                                  mainIndex: widget.mainIndex,
                                  carIndex: carIndex,
                                  onImageListChange: (imgList) =>
                                      widget.bookingProvider
                                        ..transporterDetailList
                                                .value[widget.mainIndex]
                                                .carDetails[carIndex]
                                                .fileImage =
                                            imgList
                                        ..notify(),
                                  fileImageList: fileImageList,
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
            Gap(AppSize.w16),
            AnimatedBuilder(
              animation: _pageController,
              builder: (context, child) {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    for (
                      var i = 0;
                      i <
                          widget
                              .bookingProvider
                              .transporterDetailList
                              .value[widget.mainIndex]
                              .carDetails
                              .length;
                      i++
                    )
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: AppSize.w4),
                        width: AppSize.h8,
                        height: AppSize.h8,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: i == (_pageController.page?.round() ?? 0)
                              ? Colors.blue
                              : Colors.grey[400],
                        ),
                      ),
                  ],
                );
              },
            ),
            Gap(AppSize.h16),
            if (widget.onClose != null)
              GestureDetector(
                onTap: () {
                  setState(() {
                    isOpen = !isOpen;
                  });
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  spacing: AppSize.w4,
                  children: [
                    const Icon(Icons.close, color: AppColors.primaryColor),
                    Text(
                      context.l10n.close,
                      style: context.textTheme.bodyLarge?.copyWith(
                        color: context.theme.primaryColor,
                        fontWeight: FontWeight.w600,
                        fontSize: AppSize.sp16,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ],
      ),
    );
  }
}
