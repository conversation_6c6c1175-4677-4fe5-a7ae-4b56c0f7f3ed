import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/db/app_db.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/presentation/modules/auth_module/check_otp_page/models/check_otp_params.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/shared/repositories/account_repository.dart';
import 'package:transport_match/shared/rest_api/rest_api.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/utils/logger.dart';

/// Login Provider
class LoginProvider extends ChangeNotifier {
  final isShowLoader = ValueNotifier<bool>(false);
  final emailController = TextEditingController(
    text: kDebugMode ? '<EMAIL>' : '',
  );
  final passwordController = TextEditingController(
    text: kDebugMode ? 'Test@123' : '',
  );
  final formKeyLogin = GlobalKey<FormState>();

  void notify() {
    if (_isClosed) return;
    notifyListeners();
  }

  final isPasswordShow = ValueNotifier(false);
  CancelToken? loginCancelToken;
  bool _isClosed = false;

  Future<void> loginAPICall({required BuildContext context}) async {
    if (_isClosed) return;
    try {
      isShowLoader.value = true;
      loginCancelToken?.cancel();
      loginCancelToken = CancelToken();

      // Time consuming operations with _isClosed check
      if (_isClosed) return;
      final deviceId = await AppCommonFunctions.getDeviceId();

      if (_isClosed) return;
      final registrationId = await AppCommonFunctions.getFcmToken();

      if (_isClosed) return;
      Map<String, dynamic> data;
      data = {
        ApiKeys.email: emailController.text.trim().toLowerCase(),
        ApiKeys.password: passwordController.text.trim(),
        ApiKeys.deviceId: deviceId,
        ApiKeys.deviceType: Platform.isAndroid
            ? DeviceType.ANDROID.name.toUpperCase()
            : Platform.isIOS
            ? DeviceType.IOS.name.toUpperCase()
            : 'UNKNOWN',
        ApiKeys.registrationId: registrationId,
      };
      final request = ApiRequest(
        path: EndPoints.signin,
        data: data,
        cancelToken: loginCancelToken,
      );

      if (_isClosed) return;
      final res = await Injector.instance<AccountRepository>().login(request);
      await res.when(
        success: (data) async {
          if (_isClosed || (loginCancelToken?.isCancelled ?? false)) return;
          isShowLoader.value = false;
          if (data.access != null && data.refresh != null) {
            if(data.user?.role == UserType.Customer.name) {
              await AppNavigationService.pushAndRemoveAllPreviousRoute(
                context,
                AppRoutes.homeBase,
                isBaseRoute: true,
              );
            } else {
              'Invalid credentials!'.showErrorAlert();
            }
            Injector.instance<AppDB>().userModel = data;
            Injector.instance<AppDB>().token = data.access ?? '';
            Injector.instance<AppDB>().refreshToken = data.refresh ?? '';
            Injector.instance<AppDB>().token.logTime;
            Injector.instance<AppDB>().refreshToken.logTime;


          } else {
            await AppNavigationService.pushNamed(
              context,
              AppRoutes.authCheckOtpScreen,
              extra: CheckOtpParams(
                email: emailController.text.trim().toLowerCase(),
              ),
            );
          }
          notify();
        },
        error: (exception) {
          if (_isClosed || (loginCancelToken?.isCancelled ?? false)) return;
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (loginCancelToken?.isCancelled ?? false)) return;
      isShowLoader.value = false;
      'loginAPICall error: $e'.logE;
    }
  }

  @override
  void dispose() {
    _isClosed = true;
    loginCancelToken?.cancel();
    emailController.dispose();
    passwordController.dispose();
    isShowLoader.dispose();
    isPasswordShow.dispose();
    super.dispose();
  }
}
