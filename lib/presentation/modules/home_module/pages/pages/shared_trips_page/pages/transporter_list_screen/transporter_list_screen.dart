import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/stock_locations_page/models/stock_locations_params.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/shared_trips_page/pages/transporter_list_screen/models/transporter_list_params.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/shared_trips_page/pages/transporter_list_screen/provider/transporter_list_provider.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/shared_trips_page/pages/transporter_list_screen/widgets/add_to_cart_sheet.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/shared_trips_page/pages/transporter_list_screen/widgets/show_filter_sheet.dart';
import 'package:transport_match/presentation/modules/home_module/provider/home_provider.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/logger.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/assign_car_widget.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';
import 'package:transport_match/widgets/location_info_widget.dart';
import 'package:transport_match/widgets/transporter_card.dart';

/// Transporter listing UI
class TransporterListScreen extends StatelessWidget {
  /// Constructor
  const TransporterListScreen({super.key, this.transporterListParams});
  final TransporterListParams? transporterListParams;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => TransporterListProvider(),
      child: ChangeNotifierProvider.value(
        value: transporterListParams?.homeProvider,
        builder: (context, child) {
          final homeProvider = context.read<HomeProvider>();
          final transporterListProvider = context
              .read<TransporterListProvider>();
          return PopScope(
            onPopInvokedWithResult: (didPop, result) =>
                homeProvider.cancelApiToken(homeProvider.findTransporterToken),
            child: Scaffold(
              backgroundColor: AppColors.ffF8F9FA,
              bottomNavigationBar: ValueListenableBuilder(
                valueListenable: homeProvider.totalAssignCar,
                builder: (context, value, child) {
                  return AppButton(
                    text: context.l10n.continues,
                    buttonColor: value == 0 ? AppColors.unSelectedColor : null,
                    onPressed: () {
                      if (value != 0) {
                        AppNavigationService.pushNamed(
                          context,
                          AppRoutes.stockLocationsScreen,
                          extra: StockLocationsParams(
                            homeProvider: homeProvider,
                            transporterDetail: transporterListProvider
                                .transporterDetailList
                                .value,
                            bookingSessionId:
                                transporterListProvider.bookingSessionId ?? '',
                            oldId: transporterListParams?.oldId,
                          ),
                        );
                        // Navigator.push(
                        //   context,
                        //   MaterialPageRoute(
                        //     builder: (context) => StockLocationsScreen(
                        //       homeProvider: homeProvider,
                        //       transporterDetail: transporterListProvider
                        //           .transporterDetailList
                        //           .value,
                        //       bookingSessionId:
                        //           transporterListProvider.bookingSessionId ??
                        //           '',
                        //       oldId: transporterListParams?.oldId,
                        //     ),
                        //   ),
                        // );
                        // AppNavigationService.pushNamed(
                        //   context,
                        //   StockLocationsPage(
                        //     homeProvider: homeProvider,
                        //     transporterDetail:
                        //         tripController.transporterDetailList.value,
                        //     bookingSessionId:
                        //         tripController.bookingSessionId ?? '',
                        //     oldId: oldId,
                        //   ),
                        // );
                      }
                    },
                  );
                },
              ),
              appBar: CustomAppBar(title: context.l10n.transportList),
              body: ValueListenableBuilder(
                valueListenable: homeProvider.isShowLoader,
                builder: (contexts, isLoad, mainChild) => AppLoader(
                  isShowLoader: isLoad,
                  child: ValueListenableBuilder(
                    valueListenable: homeProvider.findTransporterNextUrl,
                    builder: (contexts, findTransporterNextUrl, child) {
                      return EasyRefresh(
                        triggerAxis: Axis.vertical,
                        header: AppCommonFunctions.getLoadingHeader(),
                        footer: AppCommonFunctions.getLoadingFooter(),
                        controller: context
                            .read<TransporterListProvider>()
                            .refresherController,
                        onRefresh: () async => homeProvider.findTransporter(
                          context,
                          homeProvider,
                          isFilter: true,
                        ),
                        onLoad: () async =>
                            findTransporterNextUrl.isNotEmptyAndNotNull
                            ? homeProvider.findTransporter(
                                context,
                                homeProvider,
                                isFilter: true,
                                isPagination: true,
                                isWantShowLoader: false,
                              )
                            : null,
                        child: mainChild,
                      );
                    },
                  ),
                ),
                child: ListView(
                  padding: EdgeInsets.symmetric(horizontal: AppSize.w16),
                  children: [
                    Container(
                      padding: EdgeInsets.all(AppSize.h16),
                      margin: EdgeInsets.only(bottom: AppSize.h10),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                context.l10n.yourShipment,
                                style: context.textTheme.titleLarge,
                              ),
                              GestureDetector(
                                onTap: () => AppNavigationService.pop(context),
                                child: Text(
                                  context.l10n.edit,
                                  style: context.textTheme.bodyLarge?.copyWith(
                                    color: context.theme.primaryColor,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Gap(AppSize.h16),

                          /// Number of total vehicles
                          Text(
                            context.l10n.noOfTotalVehicle,
                            style: context.textTheme.bodySmall?.copyWith(
                              color: AppColors.ffADB5BD,
                            ),
                          ),
                          Text(
                            homeProvider.selectedVehicleInfo.length.toString(),
                            style: context.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: AppColors.ff343A40,
                            ),
                          ),
                          Gap(AppSize.h16),
                          // Locations and dates row
                          LocationInfoWidget(
                            /// user start and end location
                            startLatitude: homeProvider
                                .selectedOriginStockLocation
                                .value
                                ?.address
                                ?.latitude,
                            startLongitude: homeProvider
                                .selectedOriginStockLocation
                                .value
                                ?.address
                                ?.longitude,
                            endLatitude: homeProvider
                                .selectedDropStockLocation
                                .value
                                ?.address
                                ?.latitude,
                            endLongitude: homeProvider
                                .selectedDropStockLocation
                                .value
                                ?.address
                                ?.longitude,

                            /// date and place
                            startLocationTitle:
                                homeProvider
                                    .selectedOriginStockLocation
                                    .value
                                    ?.name ??
                                '',
                            startLocationDate:
                                homeProvider
                                    .pickupDate
                                    .value
                                    ?.monthDateFormate ??
                                '',
                            endLocationTitle:
                                homeProvider
                                    .selectedDropStockLocation
                                    .value
                                    ?.name ??
                                '',
                            endLocationDate:
                                homeProvider
                                    .deliveryDate
                                    .value
                                    ?.monthDateFormate ??
                                '',
                          ),
                        ],
                      ),
                    ),
                    ValueListenableBuilder(
                      valueListenable: homeProvider.totalAssignCar,
                      builder: (context, value, child) {
                        return AssignCarWidget(
                          value: value,
                          totalCar: homeProvider.selectedVehicleInfo.length,
                        );
                      },
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          context.l10n.chooseTransporter,
                          style: context.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w500,
                            fontSize: AppSize.sp20,
                          ),
                        ),
                        GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () => showFilterBottomSheet<void>(
                            context,
                            homeProvider,
                          ),
                          child: Text(
                            context.l10n.filterBy,
                            style: context.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              fontSize: AppSize.sp16,
                              color: AppColors.primaryColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: AppSize.h8),
                      child: ValueListenableBuilder(
                        valueListenable: homeProvider.providerList,
                        builder: (contexts, providerList, child) {
                          return Selector<HomeProvider, bool>(
                            selector: (p0, homeProvider) =>
                                homeProvider.isShowLoader.value,
                            builder: (context, isShowLoader, child) {
                              return providerList.isEmpty && !isShowLoader
                                  ? SizedBox(
                                      height: AppSize.h200,
                                      child: Center(
                                        child: Text(
                                          context.l10n.noProviderFound,
                                        ),
                                      ),
                                    )
                                  : Consumer<TransporterListProvider>(
                                      builder: (context, transporterProvider, child) {
                                        return ValueListenableBuilder(
                                          valueListenable: transporterProvider
                                              .transporterDetailList,
                                          builder: (context, transporterList, child) {
                                            return ListView.builder(
                                              itemCount: providerList.length,
                                              shrinkWrap: true,
                                              physics:
                                                  const NeverScrollableScrollPhysics(),
                                              padding: EdgeInsets.zero,
                                              itemBuilder: (contexts, index) {
                                                final data =
                                                    providerList[index];
                                                final isSelected =
                                                    transporterList.any(
                                                      (e) => e.trip == data.id,
                                                    );

                                                final isProviderDisabled =
                                                    homeProvider
                                                        .disableProvider(
                                                          context,
                                                          data,
                                                          isSelected:
                                                              isSelected,
                                                        );

                                                return GestureDetector(
                                                  behavior:
                                                      HitTestBehavior.opaque,
                                                  onTap: () {
                                                    if (isProviderDisabled.$1) {
                                                      return;
                                                    }
                                                    // Debug: Print transporter selection info
                                                    print(
                                                      'DEBUG: TransporterListScreen - Tapping transporter',
                                                    );
                                                    print(
                                                      'DEBUG: data.id: ${data.id}',
                                                    );
                                                    print(
                                                      'DEBUG: isSelected: $isSelected',
                                                    );
                                                    print(
                                                      'DEBUG: isInAllVehicle.value: ${homeProvider.isInAllVehicle.value}',
                                                    );
                                                    print(
                                                      'DEBUG: selectedVehicleInfo.length: ${homeProvider.selectedVehicleInfo.length}',
                                                    );

                                                    final matchingTransporter =
                                                        transporterList
                                                            .where(
                                                              (e) =>
                                                                  e.trip ==
                                                                  data.id,
                                                            )
                                                            .firstOrNull;
                                                    print(
                                                      'DEBUG: matchingTransporter found: ${matchingTransporter != null}',
                                                    );
                                                    if (matchingTransporter !=
                                                        null) {
                                                      print(
                                                        'DEBUG: matchingTransporter.carDetails.length: ${matchingTransporter.carDetails.length}',
                                                      );
                                                    }

                                                    final newAssignCarValue =
                                                        isSelected
                                                        ? transporterList
                                                                  .where(
                                                                    (e) =>
                                                                        e.trip ==
                                                                        data.id,
                                                                  )
                                                                  .firstOrNull
                                                                  ?.carDetails
                                                                  .length ??
                                                              0
                                                        : homeProvider
                                                              .isInAllVehicle
                                                              .value
                                                        ? homeProvider
                                                              .selectedVehicleInfo
                                                              .length
                                                        : 1;

                                                    print(
                                                      'DEBUG: Setting assignCarValue to: $newAssignCarValue',
                                                    );

                                                    homeProvider
                                                      ..assignCarValue.value =
                                                          newAssignCarValue
                                                      ..notify();
                                                    openAddToCartSheet(
                                                      context,
                                                      data,
                                                      homeProvider,
                                                      transporterProvider,
                                                    );
                                                  },
                                                  child: TransporterCard(
                                                    data: data,
                                                    isSelected: isSelected,
                                                    totalSlot:
                                                        homeProvider.totalSlots,
                                                    isDisabled:
                                                        isProviderDisabled,
                                                  ),
                                                );
                                              },
                                            );
                                          },
                                        );
                                      },
                                    );
                            },
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
