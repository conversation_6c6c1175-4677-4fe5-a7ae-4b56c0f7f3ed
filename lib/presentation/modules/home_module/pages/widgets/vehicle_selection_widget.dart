import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/models/car_creation_model.dart';
import 'package:transport_match/presentation/modules/home_module/models/pickup_cost_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/widgets/vehicle_type_widget.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/app_string.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/utils/validators/global_text_validator.dart';
import 'package:transport_match/widgets/app_confirm_check_box.dart';
import 'package:transport_match/widgets/app_dropdown.dart';
import 'package:transport_match/widgets/app_radio_button.dart';
import 'package:transport_match/widgets/app_textfield.dart';

/// Vehicle information UI
class VehicleSelectorWidget extends StatefulWidget {
  const VehicleSelectorWidget({
    required this.carBrands,
    required this.selectedBrand,
    required this.onCarBrandSelected,
    required this.carYearList,
    required this.selectedCarYear,
    required this.onCarYearSelected,
    required this.carModels,
    required this.selectedCarModel,
    required this.onCarModelSelected,
    required this.showManualInputs,
    required this.onManualInputChanged,
    required this.onCarShouldBePickedUpFromMyLocationChanged,
    this.onDeleteButtonClick,
    this.onCarBrandStringChanged,
    this.onCarModelStringChanged,
    this.onCarYearStringChanged,
    this.onCarSerialStringChanged,
    this.onCarIssueStringChanged,
    this.carBrandString,
    this.carModelString,
    this.carYearString,
    this.carIssueString,
    this.carSerialString,
    this.carType,
    super.key,
    this.onCarTypeChanged,
    this.onCarWinchRequiredChanged,
    this.isWinchRequired = false,
    this.isExclusiveTrip = false,
    this.isCarShouldBePickedUpFromMyLocation = false,
    this.costId,
    this.pickupCostModel,
    required this.modelHashCode,
  });

  final bool isExclusiveTrip;
  final List<CarCreationModel> carBrands;
  final CarCreationModel? selectedBrand;
  final void Function(CarCreationModel selectedItem) onCarBrandSelected;
  final List<CarYearModel> carYearList;
  final CarYearModel? selectedCarYear;
  final void Function(CarYearModel selectedItem) onCarYearSelected;
  final List<CarModel> carModels;
  final CarModel? selectedCarModel;
  final void Function(CarModel selectedItem) onCarModelSelected;
  final VoidCallback? onDeleteButtonClick;
  final bool showManualInputs;
  final void Function({required bool value}) onManualInputChanged;
  final void Function(String value)? onCarBrandStringChanged;
  final void Function(String value)? onCarModelStringChanged;
  final void Function(String value)? onCarYearStringChanged;
  final void Function(String value)? onCarSerialStringChanged;
  final void Function(String value)? onCarIssueStringChanged;
  final void Function(String? value)? onCarTypeChanged;
  final void Function({required bool value})? onCarWinchRequiredChanged;
  final void Function({required bool value})?
  onCarShouldBePickedUpFromMyLocationChanged;
  final String? carBrandString;
  final String? carModelString;
  final String? carYearString;
  final String? carIssueString;
  final String? carSerialString;
  final bool isWinchRequired;
  final ValueNotifier<String?>? carType;
  final ValueNotifier<int?>? costId;
  final bool isCarShouldBePickedUpFromMyLocation;
  final ValueNotifier<PickupCostModel?>? pickupCostModel;
  final int modelHashCode;

  @override
  State<VehicleSelectorWidget> createState() => _VehicleSelectorWidgetState();
}

class _VehicleSelectorWidgetState extends State<VehicleSelectorWidget> {
  final TextEditingController vehicleBrandController = TextEditingController();
  final TextEditingController vehicleModelController = TextEditingController();
  final TextEditingController vehicleYearController = TextEditingController();
  final TextEditingController vehicleIssueController = TextEditingController();
  final TextEditingController vehicleSerialController = TextEditingController();

  // Dropdown controllers
  final SingleValueDropDownController carBrandController =
      SingleValueDropDownController();
  final SingleValueDropDownController carYearController =
      SingleValueDropDownController();
  final SingleValueDropDownController carModelController =
      SingleValueDropDownController();

  // bool isExpanded = false;
  // bool hasInteractedWithIssue = false; // Track interaction for issue field
  // bool hasInteractedWithSerial = false; // Track interaction for serial field

  @override
  void initState() {
    super.initState();
    _updateControllers();
  }

  @override
  void didUpdateWidget(covariant VehicleSelectorWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    ///if modelHashCode is not same then _updateControllers
    if (oldWidget.modelHashCode != widget.modelHashCode) {
      // setState(_updateControllers);
      carBrandController.clearDropDown(notify: false);
      carYearController.clearDropDown(notify: false);
      carModelController.clearDropDown(notify: false);
      WidgetsBinding.instance.addPostFrameCallback((t) {
        vehicleIssueController.clear();
        vehicleSerialController.clear();
        vehicleBrandController.clear();
        vehicleModelController.clear();
        vehicleYearController.clear();
      });
    }
  }

  // @override
  // void didUpdateWidget(covariant VehicleSelectorWidget oldWidget) {
  //   super.didUpdateWidget(oldWidget);
  //   if (oldWidget.carBrandString != widget.carBrandString ||
  //       oldWidget.carModelString != widget.carModelString ||
  //       oldWidget.carYearString != widget.carYearString ||
  //       oldWidget.carIssueString != widget.carIssueString ||
  //       oldWidget.carSerialString != widget.carSerialString) {
  //         print('=>${oldWidget.carBrandString}==${widget.carBrandString} : ${oldWidget.carModelString}==${widget.carModelString} : ${oldWidget.carYearString}==${widget.carYearString} : ${oldWidget.carIssueString}==${widget.carIssueString} : ${oldWidget.carSerialString}==${widget.carSerialString}');
  //     WidgetsBinding.instance.addPostFrameCallback((_) {
  //         print(
  //         '-->=>${oldWidget.carBrandString}==${widget.carBrandString} : ${oldWidget.carModelString}==${widget.carModelString} : ${oldWidget.carYearString}==${widget.carYearString} : ${oldWidget.carIssueString}==${widget.carIssueString} : ${oldWidget.carSerialString}==${widget.carSerialString}',
  //       );

  //       if (mounted) {
  //         _updateControllers();
  //         // Reset interaction state if the fields are cleared
  //         if (widget.carIssueString == null) {
  //           setState(() {
  //             hasInteractedWithIssue = false;
  //           });
  //         }
  //         if (widget.carSerialString == null) {
  //           setState(() {
  //             hasInteractedWithSerial = false;
  //           });
  //         }
  //       }
  //     });
  //   }
  // }

  void _updateControllers() {
    vehicleBrandController.text = widget.carBrandString ?? '';
    vehicleModelController.text = widget.carModelString ?? '';
    vehicleYearController.text = widget.carYearString ?? '';
    vehicleIssueController.text = widget.carIssueString ?? '';
    vehicleSerialController.text = widget.carSerialString ?? '';
    if (widget.selectedBrand != null) {
      carBrandController.setDropDown(
        DropDownValueModel(
          value: widget.selectedBrand,
          name: widget.selectedBrand?.carBrand ?? '',
        ),
      );
    } else {
      carBrandController.clearDropDown();
    }
    if (widget.selectedCarYear != null) {
      carYearController.setDropDown(
        DropDownValueModel(
          value: widget.selectedCarYear,
          name: widget.selectedCarYear?.year.toString() ?? '',
        ),
      );
    } else {
      carYearController.clearDropDown();
    }
    if (widget.selectedCarModel != null) {
      carModelController.setDropDown(
        DropDownValueModel(
          value: widget.selectedCarModel,
          name: widget.selectedCarModel?.model ?? '',
        ),
      );
    } else {
      carModelController.clearDropDown();
    }
  }

  @override
  void dispose() {
    vehicleBrandController.dispose();
    vehicleModelController.dispose();
    vehicleYearController.dispose();
    vehicleIssueController.dispose();
    vehicleSerialController.dispose();
    carBrandController.dispose();
    carYearController.dispose();
    carModelController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppSize.h16),
      width: double.maxFinite,
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppSize.r10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                context.l10n.vehicleInfo,
                style: context.textTheme.titleLarge,
              ),
              if (widget.onDeleteButtonClick != null)
                GestureDetector(
                  onTap: () {
                    widget.onDeleteButtonClick?.call();
                  },
                  child: AppAssets.iconsDelete.image(
                    width: AppSize.h24,
                    height: AppSize.h24,
                  ),
                ),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Gap(AppSize.h16),
              if (!widget.showManualInputs) ...[
                AppDropdown(
                  controller: carBrandController,
                  items: widget.carBrands
                      .map(
                        (e) => DropDownValueModel(
                          name: e.carBrand ?? '',
                          value: e,
                        ),
                      )
                      .toList(),
                  title: context.l10n.vehicleBrand,
                  validator: (p0) => commonValidator(
                    errorMessage: context.l10n.enterVBrandName,
                    inputValue: p0,
                  ),
                  onChanged: (p0) {
                    if (p0 is DropDownValueModel && p0.value != null) {
                      widget.onCarBrandSelected(p0.value as CarCreationModel);

                      // Update controller when selectedBrand changes
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        if (widget.selectedBrand != null) {
                          carBrandController.setDropDown(
                            DropDownValueModel(
                              value: widget.selectedBrand,
                              name: widget.selectedBrand?.carBrand ?? '',
                            ),
                          );
                        } else {
                          carBrandController.clearDropDown();
                        }
                        carYearController.clearDropDown();
                        carModelController.clearDropDown();
                      });
                    }
                  },
                  hintText: context.l10n.chooseVehicleBrand,
                ),
                Padding(
                  padding: EdgeInsets.symmetric(vertical: AppSize.h16),
                  child: AppDropdown(
                    controller: carYearController,
                    items: widget.carYearList
                        .map(
                          (e) => DropDownValueModel(
                            name: e.year.toString(),
                            value: e,
                          ),
                        )
                        .toList(),
                    title: context.l10n.vehicleYear,
                    validator: (p0) => commonValidator(
                      errorMessage: context.l10n.chooseVYear,
                      inputValue: p0,
                    ),
                    onChanged: (p0) {
                      if (p0 is DropDownValueModel && p0.value != null) {
                        widget.onCarYearSelected(p0.value as CarYearModel);

                        // Update controller when selectedCarYear changes
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          if (widget.selectedCarYear != null) {
                            carYearController.setDropDown(
                              DropDownValueModel(
                                value: widget.selectedCarYear,
                                name:
                                    widget.selectedCarYear?.year.toString() ??
                                    '',
                              ),
                            );
                          } else {
                            carYearController.clearDropDown();
                          }
                          carModelController.clearDropDown();
                        });
                      }
                    },
                    hintText: context.l10n.chooseVehicleYear,
                  ),
                ),
                AppDropdown(
                  controller: carModelController,
                  items: widget.carModels
                      .map((e) => DropDownValueModel(name: e.model, value: e))
                      .toList(),
                  title: context.l10n.vehicleModel,
                  validator: (p0) => commonValidator(
                    errorMessage: context.l10n.chooseVModel,
                    inputValue: p0,
                  ),
                  onChanged: (p0) {
                    if (p0 is DropDownValueModel && p0.value != null) {
                      widget.onCarModelSelected(p0.value as CarModel);

                      // Update controller when selectedCarModel changes
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        if (widget.selectedCarModel != null) {
                          carModelController.setDropDown(
                            DropDownValueModel(
                              value: widget.selectedCarModel,
                              name: widget.selectedCarModel?.model ?? '',
                            ),
                          );
                        } else {
                          carModelController.clearDropDown();
                        }
                      });
                    }
                  },
                  hintText: context.l10n.chooseVehicleModel,
                ),
              ] else ...[
                Text(
                  context.l10n.vehicleType,
                  style: context.textTheme.titleSmall,
                ),
                Gap(AppSize.h4),
                ValueListenableBuilder(
                  valueListenable: widget.carType ?? ValueNotifier(null),
                  builder: (context, value, child) {
                    return VehicleTypeWidget(
                      type: value ?? AppStrings.carHauler,
                      onChange: (value) {
                        if (value != null) {
                          widget.onCarTypeChanged?.call(value);
                        }
                      },
                    );
                  },
                ),
                AppTextFormField(
                  controller: vehicleBrandController,
                  title: context.l10n.vehicleBrand,
                  hintText: context.l10n.enterVehicleBrand,
                  textAction: TextInputAction.next,
                  validator: (p0) => commonValidator(
                    errorMessage: context.l10n.enterVBrandName,
                    inputValue: p0,
                  ),
                  onChanged: (value) {
                    if (value != null) {
                      widget.onCarBrandStringChanged?.call(value);
                    }
                  },
                ),
                Padding(
                  padding: EdgeInsets.symmetric(vertical: AppSize.h16),
                  child: AppTextFormField(
                    controller: vehicleYearController,
                    title: context.l10n.vehicleYear,
                    hintText: context.l10n.enterVehicleYearLabel,
                    textAction: TextInputAction.next,
                    keyboardType: TextInputType.phone,
                    validator: (p0) => commonValidator(
                      errorMessage: context.l10n.enterVYear,
                      inputValue: p0,
                    ),
                    readOnly: true,
                    onTap: () async {
                      await showDialog(
                        context: context,
                        builder: (context) {
                          return AlertDialog(
                            title: Text(
                              context.l10n.vehicleYear,
                              style: context.textTheme.titleSmall,
                            ),
                            content: SizedBox(
                              width: AppSize.sp300,
                              height: AppSize.sp300,
                              child: YearPicker(
                                firstDate: DateTime(1900),
                                lastDate:
                                    vehicleYearController
                                        .text
                                        .isNotEmptyAndNotNull
                                    ? DateTime(
                                        int.parse(vehicleYearController.text),
                                      )
                                    : DateTime.now(),
                                selectedDate:
                                    vehicleYearController
                                        .text
                                        .isNotEmptyAndNotNull
                                    ? DateTime(
                                        int.parse(vehicleYearController.text),
                                      )
                                    : DateTime.now(),
                                onChanged: (DateTime dateTime) {
                                  Navigator.pop(context);
                                  vehicleYearController.text = dateTime.year
                                      .toString();
                                  widget.onCarYearStringChanged?.call(
                                    dateTime.year.toString(),
                                  );
                                },
                              ),
                            ),
                          );
                        },
                      );
                    },
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    maxTextLength: 4,
                  ),
                ),
                AppTextFormField(
                  title: context.l10n.vehicleModel,
                  controller: vehicleModelController,
                  validator: (p0) => commonValidator(
                    errorMessage: context.l10n.enterVModel,
                    inputValue: p0,
                  ),
                  hintText: context.l10n.enterVehicleModelLabel,
                  textAction: TextInputAction.next,
                  onChanged: (value) {
                    if (value != null) {
                      widget.onCarModelStringChanged?.call(value);
                    }
                  },
                ),
              ],
              Padding(
                padding: EdgeInsets.symmetric(vertical: AppSize.h16),
                child: AppTextFormField(
                  title: context.l10n.pleaseDescribeTheIssue,
                  controller: vehicleIssueController,
                  hintText: context.l10n.writeIssueDetailsHere,
                  labelColor: AppColors.ffADB5BD,
                  textAction: TextInputAction.next,
                  // autovalidateMode: hasInteractedWithIssue
                  //     ? AutovalidateMode.onUserInteraction
                  //     : AutovalidateMode.disabled,
                  onChanged: (value) {
                    // setState(() {
                    //   hasInteractedWithIssue = true;
                    // });
                    if (value != null) {
                      widget.onCarIssueStringChanged?.call(value);
                    }
                  },
                ),
              ),
              AppTextFormField(
                controller: vehicleSerialController,
                title: context.l10n.vehicleSerialNo,
                maxTextLength: 100,
                hintText: context.l10n.vehicleSerialNumber,
                validator: (p0) => commonValidator(
                  errorMessage: context.l10n.pleaseEnterVSerialNumber,
                  inputValue: p0,
                ),
                labelColor: AppColors.ffADB5BD,
                // autovalidateMode: hasInteractedWithSerial
                //     ? AutovalidateMode.onUserInteraction
                //     : AutovalidateMode.disabled,
                onChanged: (value) {
                  // setState(() {
                  //   hasInteractedWithSerial = true;
                  // });
                  if (value != null) {
                    widget.onCarSerialStringChanged?.call(value);
                  }
                },
              ),
              if (!widget.isExclusiveTrip) ...[
                Padding(
                  padding: EdgeInsets.symmetric(vertical: AppSize.h16),
                  child: AppConfirmCheckBox(
                    description: context.l10n.winchRequired,
                    value: widget.isWinchRequired,
                    onSelectionChanged: ({required bool value}) {
                      if (value &&
                          !widget.isCarShouldBePickedUpFromMyLocation) {
                        widget.onCarShouldBePickedUpFromMyLocationChanged?.call(
                          value: value,
                        );
                      }
                      if (widget.costId != null &&
                          widget.costId?.value !=
                              widget.pickupCostModel?.value?.results.first.id) {
                        widget.costId?.value =
                            widget.pickupCostModel?.value?.results.first.id;
                      }
                      widget.onCarWinchRequiredChanged?.call(value: value);
                    },
                  ),
                ),
                AppConfirmCheckBox(
                  value: widget.isCarShouldBePickedUpFromMyLocation,
                  description: context
                      .l10n
                      .iNeedMyCarToBePickedUpAndTakenToTheStockLocation,
                  isDisabledValueChange: widget.isWinchRequired,
                  onSelectionChanged: ({required bool value}) {
                    if (widget.isWinchRequired && !value) {
                      context
                          .l10n
                          .youHaveOptForWinchServiceSoYouCanNotDisableThisOption
                          .showInfoAlert();
                    } else {
                      widget.onCarShouldBePickedUpFromMyLocationChanged?.call(
                        value: value,
                      );
                    }
                  },
                ),
              ],
              if (widget.isCarShouldBePickedUpFromMyLocation)
                ValueListenableBuilder(
                  valueListenable:
                      widget.pickupCostModel ?? ValueNotifier(null),
                  builder: (context, pickupCostModel, child) {
                    if (widget.costId != null) {
                      return Column(
                        spacing: AppSize.h12,
                        children: [
                          ValueListenableBuilder(
                            valueListenable:
                                widget.costId ?? ValueNotifier(null),
                            builder: (context, value, child) {
                              return pickupCostModel != null &&
                                      pickupCostModel.results.isNotEmpty
                                  ? SizedBox(
                                      width: context.width,
                                      child: GridView.builder(
                                        shrinkWrap: true,
                                        padding: EdgeInsets.only(
                                          top: AppSize.h10,
                                        ),
                                        itemCount:
                                            pickupCostModel.results.length,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        gridDelegate:
                                            const SliverGridDelegateWithFixedCrossAxisCount(
                                              crossAxisCount: 2,
                                              childAspectRatio: 2,
                                            ),
                                        itemBuilder: (context, pickupCostIndex) {
                                          final result = pickupCostModel
                                              .results[pickupCostIndex];
                                          final isDisabled =
                                              widget.isWinchRequired &&
                                              pickupCostIndex == 1;

                                          return Opacity(
                                            opacity: isDisabled ? 0.5 : 1.0,
                                            child: IgnorePointer(
                                              ignoring: isDisabled,
                                              child: AppRadioButton(
                                                index: pickupCostIndex,
                                                title: result.serviceType
                                                    .replaceAll('_', ' ')
                                                    .capitalized,
                                                subtitle:
                                                    '\$ ${result.minimumFee}',
                                                subtitle2:
                                                    '\$ ${result.serviceFee}/km',
                                                isSelected: value == result.id,
                                                onChanged: (index) {
                                                  if (!isDisabled) {
                                                    widget.costId?.value =
                                                        result.id;
                                                  }
                                                },
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    )
                                  : const SizedBox();
                            },
                          ),
                          Text.rich(
                            TextSpan(
                              children: [
                                TextSpan(
                                  text: '${context.l10n.note}: ',
                                  style: context.textTheme.bodySmall?.copyWith(
                                    fontWeight: FontWeight.w700,
                                    color: AppColors.ff6C757D,
                                  ),
                                ),
                                TextSpan(
                                  text: context
                                      .l10n
                                      .asWeHaveTwoDifferentPricingWeWillTakeTheLargestSumAmount,
                                  style: context.textTheme.bodySmall?.copyWith(
                                    fontWeight: FontWeight.w400,
                                    color: AppColors.ff6C757D,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      );
                    }
                    return const SizedBox();
                  },
                ),
              Gap(AppSize.h16),
              AppConfirmCheckBox(
                description: context.l10n.didNtFoundCar,
                onSelectionChanged: ({required bool value}) {
                  widget.onManualInputChanged(value: value);

                  /// clear controllers if value is true
                  if (value) {
                    carBrandController.clearDropDown(notify: false);
                    carYearController.clearDropDown(notify: false);
                    carModelController.clearDropDown(notify: false);
                  } else {
                    vehicleBrandController.clear();
                    vehicleModelController.clear();
                    vehicleYearController.clear();
                    // vehicleIssueController.clear();
                    // vehicleSerialController.clear();
                  }
                },
                value: widget.showManualInputs,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
