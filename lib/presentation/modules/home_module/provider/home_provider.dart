import 'dart:async';
import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/models/car_creation_model.dart';
import 'package:transport_match/presentation/modules/home_module/models/pickup_cost_model.dart';
import 'package:transport_match/presentation/modules/home_module/models/provider_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/req_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/selected_vehicle_info.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/empty_transporter_screen/models/empty_transporter_params.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/exclusive_trip_screen/models/stock_data_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/shared_trips_page/pages/transporter_list_screen/models/transporter_list_params.dart';
import 'package:transport_match/presentation/modules/home_module/provider/places_api_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/shared/repositories/home_repository.dart';
import 'package:transport_match/shared/rest_api/api_keys.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/app_string.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/utils/logger.dart';

/// Home Module Data Handling
class HomeProvider extends ChangeNotifier {
  HomeProvider({TripModel? data, bool isExclusive = false}) {
    if (data == null) {
      selectedVehicleInfo.add(SelectedVehicleInfoModel());
    } else {
      isAssignDataLoad.value = true;
    }
    getCarBrands().then((value) {
      if (data != null) {
        assignData(data, isExclusive: isExclusive);
      } else {
        isAssignDataLoad.value = false;
      }
    });
  }
  ExclusiveReqModel? exclusiveReqModel;
  bool _isClosed = false;

  final formKeyExclusiveTrip = GlobalKey<FormState>();
  final formKeySharedTrip = GlobalKey<FormState>();
  TextEditingController originController = TextEditingController();
  TextEditingController dropController = TextEditingController();
  TextEditingController sharedOriginController = TextEditingController();
  TextEditingController sharedDropController = TextEditingController();

  SingleValueDropDownController selectedOriginStockLocationController =
      SingleValueDropDownController();
  SingleValueDropDownController selectedDropStockLocationController =
      SingleValueDropDownController();

  final originStockLocationList = ValueNotifier<List<StopLocationData>>([]);
  final dropStockLocationList = ValueNotifier<List<StopLocationData>>([]);
  final selectedOriginStockLocation = ValueNotifier<StopLocationData?>(null);
  final selectedDropStockLocation = ValueNotifier<StopLocationData?>(null);
  final providerList = ValueNotifier<List<ProviderListData>>([]);
  final exclusivePickLocation = ValueNotifier<AddressModel?>(null);
  final exclusiveDropLocation = ValueNotifier<AddressModel?>(null);
  final pickupDate = ValueNotifier<DateTime?>(null);
  final deliveryDate = ValueNotifier<DateTime?>(null);
  final isInAllVehicle = ValueNotifier<bool>(false);
  final winchRequired = ValueNotifier<bool>(false);
  final oldId = ValueNotifier<int?>(null);

  /// car slot assigning variables
  final assignCarValue = ValueNotifier<int>(0);
  final totalAssignCar = ValueNotifier<int>(0);

  /// filter sheet variables
  final isSearchAddressShowLoader = ValueNotifier(false);
  final rate = ValueNotifier<String>('');
  final isLowest = ValueNotifier<bool>(false);
  final is2Days = ValueNotifier<bool>(false);
  final isWinch = ValueNotifier<bool>(false);
  final isWinchFix = ValueNotifier<bool>(false);
  final isException = ValueNotifier<bool>(false);
  final isExceptionFix = ValueNotifier<bool>(false);

  /// Trip repository
  final HomeRepository homeRepository = Injector.instance<HomeRepository>();

  /// Origin location controller
  final originLocationController = ValueNotifier<AddressModel?>(null);

  /// Destination location controller
  final dropLocationController = ValueNotifier<AddressModel?>(null);

  /// Value notifier for loading
  final isShowLoader = ValueNotifier(false);

  // ValueNotifier<List<InsuranceModel>> insuranceList = ValueNotifier([]);

  /// Car brands list
  List<CarCreationModel> carBrands = [];

  /// Vehicle information
  final selectedVehicleInfo = <SelectedVehicleInfoModel>[];

  /// Vehicle pickup selection flag
  bool _isPickUpSelected = false;

  void changeFilterRate(String value) {
    if (value == rate.value) {
      rate.value = '';
    } else {
      rate.value = value;
    }
    notify();
  }

  void clearFilter() {
    rate.value = '';
    is2Days.value = false;
    isLowest.value = false;
    notify();
  }

  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'notify error: $e'.logE;
    }
  }

  void resetSharedControllers() {
    originStockLocationList.value.clear();
    dropStockLocationList.value.clear();

    originLocationController.value = null;
    dropLocationController.value = null;

    selectedOriginStockLocation.value = null;
    selectedDropStockLocation.value = null;

    sharedOriginController.clear();
    sharedDropController.clear();

    selectedOriginStockLocationController.clearDropDown();
    selectedDropStockLocationController.clearDropDown();

    selectedVehicleInfo
      ..clear()
      ..add(SelectedVehicleInfoModel());
    pickUpAddress.value = null;
    pickupDate.value = null;
    deliveryDate.value = null;
    notify();
  }

  void resetControllers() {
    originController.dispose(); // Clean up old controller
    originController = TextEditingController(); // New instance
    dropController.dispose(); // Clean up old controller
    dropController = TextEditingController(); // New instance
    notify();
  }

  /// below function is to get total
  /// slots of all selected vehicles
  num get totalSlots {
    num total = 0;
    for (final vehicle in selectedVehicleInfo) {
      if (vehicle.selectedCarModel != null) {
        total += vehicle.selectedCarModel?.sizes?.size ?? 0;
      }
    }
    return total;
  }

  CancelToken? listNearCancelToken;
  Future<List<StopLocationData>> listNearByLocation({
    required BuildContext context,
    bool isDrop = false,
    required LatLng latLng,
  }) async {
    final completer = Completer<List<StopLocationData>>();
    if (_isClosed) return [];

    try {
      isSearchAddressShowLoader.value = true;
      listNearCancelToken?.cancel();
      listNearCancelToken = CancelToken();
      Map<String, dynamic> data;
      data = {
        ApiKeys.latitude: latLng.latitude,
        ApiKeys.longitude: latLng.longitude,
      };
      final request = ApiRequest(
        path: EndPoints.listNearByLocation,
        params: data,
        cancelToken: listNearCancelToken,
      );
      final res = await homeRepository.listNearByLocation(request);
      res.when(
        success: (data) async {
          if (_isClosed || (listNearCancelToken?.isCancelled ?? true)) {
            return false;
          }
          isSearchAddressShowLoader.value = false;
          final dummyList = <StopLocationData>[];
          for (final stock in data.stockLocation) {
            if (!dummyList.any((element) => element.name == stock.name) &&
                !dummyList.any((element) => element.id == stock.id)) {
              dummyList.add(stock);
            }
          }
          completer.complete(dummyList);
          if (isDrop) {
            dropStockLocationList.value
              ..clear()
              ..addAll(dummyList);
            dropStockLocationList.notifyListeners();
            selectedDropStockLocation.value = null;
            dropLocationController.value = null;
            // dropStockLocationList.value = dummyList;
          } else {
            originStockLocationList.value
              ..clear()
              ..addAll(dummyList);
            originStockLocationList.notifyListeners();
            originLocationController.value = null;
            selectedOriginStockLocation.value = null;
          }
          if (dummyList.isEmpty) {
            context.l10n.noStockLocationFound.showErrorAlert();
          }
          notify();
        },
        error: (exception) {
          if (_isClosed || (listNearCancelToken?.isCancelled ?? true)) {
            return [];
          }
          isSearchAddressShowLoader.value = false;
          completer.complete([]);
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (listNearCancelToken?.isCancelled ?? true)) return [];
      isSearchAddressShowLoader.value = false;
      completer.complete([]);
      'listNearByLocation error: $e'.logE;
    }
    return completer.future;
  }

  /// Pick up location selected
  bool get isPickUpSelected => _isPickUpSelected;
  final pickUpAddress = ValueNotifier<AddressModel?>(null);
  // final costId = ValueNotifier<int?>(null);
  PickupCostModel? pickupCostModel;
  CancelToken? getCostOfPickUpToken;
  Future<void> getCostOfPickUp({bool isClear = true}) async {
    if (_isClosed) return;
    try {
      // Check if any vehicle has pickup enabled
      final hasPickupEnabled = selectedVehicleInfo.any(
        (e) => e.isCarShouldBePickedUpFromMyLocation,
      );
      if (!hasPickupEnabled) {
        if (_isClosed) return;
        for (final vehicle in selectedVehicleInfo) {
          vehicle.pickupCostModel?.value = null;
          if (isClear) vehicle.costId?.value = null;
        }
        if (selectedVehicleInfo.any(
          (e) => e.isCarShouldBePickedUpFromMyLocation,
        )) {
          isPickUpSelected = true;
        } else {
          isPickUpSelected = false;
        }
        return;
      }

      if (_isClosed) return;
      getCostOfPickUpToken?.cancel();
      getCostOfPickUpToken = CancelToken();
      isShowLoader.value = true;
      final response = await homeRepository.getCostOfPickUp(
        ApiRequest(
          path: EndPoints.getCostOfPickUp,
          cancelToken: getCostOfPickUpToken,
        ),
      );
      response.when(
        success: (data) {
          if (_isClosed || (getCostOfPickUpToken?.isCancelled ?? false)) return;
          // Update pickup cost model for each vehicle that has pickup enabled
          for (final vehicle in selectedVehicleInfo) {
            if (vehicle.isCarShouldBePickedUpFromMyLocation) {
              vehicle.pickupCostModel ??= ValueNotifier(null);
              vehicle.costId ??= ValueNotifier(null);
              vehicle.pickupCostModel?.value = data;
              vehicle.costId?.value ??= data.results.first.id;
            }
          }
          if (selectedVehicleInfo.any((e) => e.costId != null)) {
            isPickUpSelected = true;
          } else {
            isPickUpSelected = false;
          }
          notify();
        },
        error: (error) {
          if (_isClosed || (getCostOfPickUpToken?.isCancelled ?? false)) return;
          error.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (getCostOfPickUpToken?.isCancelled ?? false)) return;
      'getCostOfPickUp error: $e'.logE;
    } finally {
      isShowLoader.value = false;
    }
  }

  /// Set pick up location selected
  set isPickUpSelected(bool value) {
    _isPickUpSelected = value;
    notify();
  }

  /// Get car brands list api
  CancelToken? getCarCancelToken;
  Future<void> getCarBrands() async {
    if (_isClosed) return;
    isShowLoader.value = true;
    getCarCancelToken?.cancel();
    getCarCancelToken = CancelToken();
    try {
      final response = await homeRepository.getCarListForCreation(
        ApiRequest(cancelToken: getCarCancelToken),
      );
      isShowLoader.value = false;
      response.when(
        success: (data) {
          if (_isClosed || (getCarCancelToken?.isCancelled ?? true)) return;
          carBrands = data;
          notify();
        },
        error: (error) {
          if (_isClosed || (getCarCancelToken?.isCancelled ?? true)) return;
          error.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (getCarCancelToken?.isCancelled ?? true)) return;
      isShowLoader.value = false;
      'getCarBrands error: $e'.logE;
    }
  }

  /// Gets called on car brand selection
  void onCarBrandSelected(CarCreationModel selectedItem, int index) {
    if (_isClosed) return;
    selectedVehicleInfo[index].carYearList = null;
    selectedVehicleInfo[index].selectedCarYear = null;
    selectedVehicleInfo[index].carModels = null;
    selectedVehicleInfo[index].selectedCarModel = null;
    selectedVehicleInfo[index].carType = null;
    selectedVehicleInfo[index].costId = null;
    // _getCarListForCreation(selectedItem.id, index);

    selectedVehicleInfo[index].selectedBrand = CarCreationModel(
      carBrand: selectedItem.carBrand,
      years: selectedItem.years,
      id: selectedItem.id,
    );
    selectedVehicleInfo[index].carYearList =
        carBrands
            .where((element) => element.id == selectedItem.id)
            .firstOrNull
            ?.years ??
        [];
    notify();
  }

  /// Gets called on car year selection
  void onCarYearSelected(CarYearModel selectedItem, int index) {
    if (_isClosed) return;
    selectedVehicleInfo[index].carModels?.clear();
    selectedVehicleInfo[index].selectedCarModel = null;
    selectedVehicleInfo[index].carModels = List<CarModel>.from(
      selectedItem.models ?? [],
    );
    selectedVehicleInfo[index].selectedCarYear = selectedItem;
    notify();
  }

  /// Gets called on car model selection
  void onCarModelSelected(CarModel selectedItem, int index) {
    if (_isClosed) return;
    selectedVehicleInfo[index].selectedCarModel = selectedItem;
    notify();
  }

  /// Gets called on vehicle type selection
  // ignore: avoid_positional_boolean_parameters
  void onManualInputChanged(bool value, int index) {
    if (_isClosed) return;
    selectedVehicleInfo[index].selectedBrand = null;
    selectedVehicleInfo[index].carBrandString = null;
    selectedVehicleInfo[index].carModelString = null;
    selectedVehicleInfo[index].selectedCarModel = null;
    selectedVehicleInfo[index].selectedCarYear = null;
    selectedVehicleInfo[index].carYearString = null;
    selectedVehicleInfo[index].carType = value ? ValueNotifier(null) : null;
    selectedVehicleInfo[index].costId = value ? ValueNotifier(null) : null;
    selectedVehicleInfo[index].pickupCostModel = value
        ? ValueNotifier(null)
        : null;
    selectedVehicleInfo[index].carYearList = null;
    selectedVehicleInfo[index].carModels = null;
    selectedVehicleInfo[index].showManualInputs = value;
    selectedVehicleInfo[index].isCarShouldBePickedUpFromMyLocation = false;
    notify();
  }

  /// Add vehicle
  void addVehicle() {
    if (_isClosed) return;
    selectedVehicleInfo.add(
      SelectedVehicleInfoModel(
        costId: ValueNotifier(null),
        pickupCostModel: ValueNotifier(null),
      ),
    );
    notify();
  }

  /// Remove vehicle
  void removeVehicle(int index) {
    if (_isClosed) return;
    selectedVehicleInfo.removeAt(index);
    notify();
  }

  /// below function is use for clear all selected vehicle
  /// isAssignedCar assign by default as false
  void clearSelectedVehicle() {
    if (_isClosed) return;
    for (var i = 0; i < selectedVehicleInfo.length; i++) {
      selectedVehicleInfo[i].isCarAssigned = ValueNotifier(false);
    }
  }

  /// below function is use to assign isAssignedCar
  /// variable to true if that car is selected
  void assignCar(List<CarDetailModel> selectedCarList) {
    if (_isClosed) return;
    for (final car in selectedCarList) {
      final index = selectedVehicleInfo.indexWhere(
        (e) => e.selectedCarModel?.sizes?.id == car.car,
      );
      if (index != -1) {
        selectedVehicleInfo[index].isCarAssigned = ValueNotifier(true);
      }
    }
    notify();
  }

  /// below function is use to disable provider based
  /// on the user' selected vehicles, number of assigned
  /// vehicle, remain vehicle sizes, vehicle' winch and exception support.
  (bool, String) disableProvider(
    BuildContext context,
    ProviderListData data, {
    required bool isSelected,
  }) {
    var isDisabled = false;
    var message = '';

    /// get remaining vehicle list
    final remainVehicleList = selectedVehicleInfo
        .where((e) => !(e.isCarAssigned?.value ?? false))
        .toList();

    /// disable provider based on the vehicle and provider winch required
    if (!data.isWinch &&
        !remainVehicleList.any((element) => !element.isWinchRequired) &&
        !isSelected) {
      isDisabled = true;

      message = context.l10n.transporterDoesNotHave;
    }

    /// disable provider based on the vehicle and provider exception support
    if (!data.isExceptionsSupported &&
        !remainVehicleList.any(
          (element) => !(element.selectedCarModel?.sizes?.exceptions ?? true),
        ) &&
        !isSelected) {
      isDisabled = true;

      message = context.l10n.transporterDoesNotMeet;
    }

    /// disable provider based on the vehicle and provider available size
    if (!remainVehicleList.any(
          (vehicle) =>
              (vehicle.selectedCarModel?.sizes?.size ?? 0) <=
              (data.availableSlot ?? 0),
        ) &&
        !isSelected) {
      isDisabled = true;

      message = context.l10n.noAvailableSlot;
    }

    /// disable provider based on the user selected vehicle
    if (totalAssignCar.value == selectedVehicleInfo.length && !isSelected) {
      isDisabled = true;

      message = context.l10n.allVehicleAreAlready;
    }
    return (isDisabled, message);
  }

  String? getDuplicateSerial(List<SelectedVehicleInfoModel> vehicles) {
    final serials = <String>{};
    for (final vehicle in vehicles) {
      final serial = vehicle.carSerialString?.trim();
      if (serial != null && serial.isNotEmpty) {
        if (serials.contains(serial)) return serial;
        serials.add(serial);
      }
    }
    return null;
  }

  /// for finding transporter
  CancelToken? findTransporterToken;
  final findTransporterNextUrl = ValueNotifier<String?>(null);
  Future<void> findTransporter(
    BuildContext context,
    HomeProvider homeProvider, {
    bool isFilter = false,
    bool isBack = false,
    bool isRested = false,
    bool isPagination = false,
    bool isWantShowLoader = true,
  }) async {
    if (_isClosed) return;
    final duplicate = getDuplicateSerial(selectedVehicleInfo);
    if (duplicate != null) {
      'Same serial number detected: $duplicate'.showErrorAlert();
      return;
    }

    if (isFilter) {
      if (isBack) {
        AppNavigationService.pop(context);
      }
    } else {
      clearFilter();
      clearSelectedVehicle();
    }

    if (selectedVehicleInfo.any((e) => e.selectedBrand == null)) {
      if (_isClosed) return;
      await createVehicleReq(context, isBack: isRested, isRested: isRested);
    } else {
      if (_isClosed) return;
      findTransporterToken?.cancel();
      findTransporterToken = CancelToken();
      isWinchFix.value =
          (selectedVehicleInfo.length == 1 &&
              selectedVehicleInfo.first.isWinchRequired) ||
          (selectedVehicleInfo.any((e) => e.isWinchRequired) &&
              isInAllVehicle.value);
      isExceptionFix.value =
          (selectedVehicleInfo.length == 1 &&
              (selectedVehicleInfo.first.selectedCarModel?.sizes?.exceptions ??
                  false)) ||
          (selectedVehicleInfo.any(
                (e) => e.selectedCarModel?.sizes?.exceptions ?? false,
              ) &&
              isInAllVehicle.value);
      num carSize = 0;
      num minCarSize = 0;
      num maxCarSize = 0;
      for (final data in selectedVehicleInfo) {
        if (_isClosed) return;
        final size = data.selectedCarModel?.sizes?.size ?? 0;
        minCarSize = minCarSize == 0
            ? size
            : minCarSize >= size
            ? size
            : minCarSize;
        maxCarSize = maxCarSize == 0
            ? size
            : maxCarSize < size
            ? size
            : minCarSize;
        carSize += size;
      }
      if (_isClosed) return;
      try {
        final temptList = [...providerList.value];
        if (!isPagination) temptList.clear();
        if (isWantShowLoader) isShowLoader.value = true;

        /// find transporter end point
        final endPoint = EndPoints.findTransporter(
          startDate: pickupDate.value?.toUtc().passDateFormate ?? '',
          minSize: minCarSize.toString(),
          maxSize: maxCarSize.toString(),
          endDate: deliveryDate.value?.toUtc().passDateFormate ?? '',
          startLocation:
              selectedOriginStockLocation.value?.id?.toString() ?? '',
          endLocation: selectedDropStockLocation.value?.id?.toString() ?? '',
          requiredSlot: carSize.toString(),
          allVehicleIn: isInAllVehicle.value
              ? isInAllVehicle.value.toString()
              : null,
          lowestPrice: isLowest.value ? isLowest.value.toString() : null,
          time: is2Days.value ? '2' : null,
          winch: isWinchFix.value
              ? 'true'
              : isWinch.value
              ? isWinch.value.toString()
              : null,
          isExceptionsSupported: isExceptionFix.value
              ? 'true'
              : isException.value
              ? isException.value.toString()
              : null,
          // price: isLowest.value ? isLowest.value.toString() : null,
          rating: rate.value.isNotEmpty
              ? rate.value.split(' ').last.replaceAll('+', '')
              : null,
        );
        endPoint.logD;
        if (_isClosed) return;
        final response = await homeRepository.findTransporter(
          ApiRequest(
            path: isPagination
                ? findTransporterNextUrl.value ?? endPoint
                : endPoint,
            cancelToken: findTransporterToken,
          ),
        );
        if (_isClosed) return;
        response.when(
          success: (data) {
            if (_isClosed || (findTransporterToken?.isCancelled ?? true)) {
              return;
            }
            data.toJson().logD;
            isShowLoader.value = false;
            temptList.addAll(data.results ?? []);
            providerList.value = temptList;
            findTransporterNextUrl.value = data.next;
            if (!isFilter) {
              totalAssignCar.value = 0;

              AppNavigationService.pushNamed<HomeProvider>(
                context,
                data.results != null && (data.results?.isNotEmpty ?? false)
                    ? AppRoutes.homeTransporterListScreen
                    : AppRoutes.homeEmptyTransporterScreen,
                extra:
                    data.results != null && (data.results?.isNotEmpty ?? false)
                    ? TransporterListParams(
                        oldId: oldId.value,
                        homeProvider: homeProvider,
                      )
                    : EmptyTransporterParams(homeProvider: this),
              );
            }
            // clearData();
            formKeySharedTrip.currentState?.reset();
            resetSharedControllers();
            notify();
          },
          error: (error) {
            if (_isClosed || (findTransporterToken?.isCancelled ?? true)) {
              return;
            }
            isShowLoader.value = false;
            error.message.showErrorAlert();
          },
        );
      } catch (e) {
        if (_isClosed || (findTransporterToken?.isCancelled ?? true)) return;
        isShowLoader.value = false;
        'findTransporter error: $e'.logE;
      }
    }
  }

  /// to book exclusive booking
  CancelToken? bookExclusiveToken;
  Future<void> bookExclusive(
    BuildContext context,
    HomeProvider homeProvider, {
    bool isBack = false,
    bool? isFromRestedDetailScreen,
    bool isRested = false,
  }) async {
    if (_isClosed) return;
    if (selectedVehicleInfo.any((e) => e.selectedBrand == null)) {
      if (_isClosed) return;
      await createVehicleReq(context, isExclusive: true, isBack: isBack);
    } else {
      if (_isClosed) return;
      bookExclusiveToken?.cancel();
      bookExclusiveToken = CancelToken();
      exclusiveReqModel = await _getExclusiveDataModel(
        isExclusive: true,
        oldId: isRested ? oldId.value : null,
      );
      if (_isClosed) return;
      if (isBack && context.mounted) AppNavigationService.pop(context);
      try {
        isShowLoader.value = true;
        final response = await homeRepository.createExclusiveTrip(
          ApiRequest(
            path: EndPoints.createExclusiveBook,
            data: exclusiveReqModel?.toJson(),
            cancelToken: bookExclusiveToken,
          ),
        );
        response.when(
          success: (data) {
            if (_isClosed || (bookExclusiveToken?.isCancelled ?? true)) return;
            isShowLoader.value = false;
            'Exclusive booking created successfully.'.showSuccessAlert();
            if (isFromRestedDetailScreen != null) {
              AppNavigationService.pop(context);
              if (isFromRestedDetailScreen) AppNavigationService.pop(context);
            }
            clearData();
            formKeyExclusiveTrip.currentState?.reset();
            // Need to reset controllers after form reset to clear stock locations
            resetControllers();
          },
          error: (error) {
            if (_isClosed || (bookExclusiveToken?.isCancelled ?? true)) return;
            isShowLoader.value = false;
            error.message.showErrorAlert();
          },
        );
      } catch (e) {
        if (_isClosed || (bookExclusiveToken?.isCancelled ?? true)) return;
        isShowLoader.value = false;
        'createVehicleReq error: $e'.logE;
      }
    }
  }

  /// for creating vehicle api
  CancelToken? createVehicleToken;
  Future<void> createVehicleReq(
    BuildContext context, {
    bool isExclusive = false,
    bool isBack = false,
    bool isRested = false,
  }) async {
    if (_isClosed) return;
    createVehicleToken?.cancel();
    createVehicleToken = CancelToken();
    exclusiveReqModel = await _getExclusiveDataModel(
      isExclusive: isExclusive,
      oldId: isRested ? oldId.value : null,
    );
    if (_isClosed) return;
    try {
      isShowLoader.value = true;
      final response = await homeRepository.createCarVerification(
        ApiRequest(
          path: EndPoints.createCarVerification,
          data: exclusiveReqModel?.toJson(),
          cancelToken: createVehicleToken,
        ),
      );
      isShowLoader.value = false;
      response.when(
        success: (data) {
          if (_isClosed || (createVehicleToken?.isCancelled ?? true)) return;
          context.l10n.bookingDataVerification.showSuccessAlert();
          if (isBack) {
            AppNavigationService.pop(context);
          } else {
            clearData();
          }
        },
        error: (error) {
          if (_isClosed || (createVehicleToken?.isCancelled ?? true)) return;
          error.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (createVehicleToken?.isCancelled ?? true)) return;
      isShowLoader.value = false;
      'createVehicleReq error: $e'.logE;
    }
  }

  Future<ExclusiveReqModel> _getExclusiveDataModel({
    required bool isExclusive,
    int? oldId,
  }) async {
    final deviceId = await AppCommonFunctions.getDeviceId();
    return ExclusiveReqModel(
      userStartLocation: isExclusive ? exclusivePickLocation.value : null,
      userEndLocation: isExclusive ? exclusiveDropLocation.value : null,
      customerStartDate: pickupDate.value!.isToday()
          ? DateTime.now()
          : pickupDate.value!,
      customerEndDate: deliveryDate.value!.isToday()
          ? DateTime.now()
          : deliveryDate.value!,
      oldId: oldId,
      allVehicleInOneTrip: isInAllVehicle.value,
      deviceId: deviceId,
      bookingType: isExclusive
          ? BookingType.EXCLUSIVE.name
          : BookingType.SHARED.name,
      endStopLocation: isExclusive
          ? null
          : selectedDropStockLocation.value?.id?.toString(),
      startStopLocation: isExclusive
          ? null
          : selectedOriginStockLocation.value?.id?.toString(),
      carDetails: selectedVehicleInfo
          .map(
            (e) => CarDetailModel(
              serialNumber: e.carSerialString,
              car: e.selectedCarModel?.sizes?.id,
              carName: e.carModelString ?? e.selectedCarModel?.model,
              brand: e.carBrandString ?? e.selectedBrand?.id?.toString(),
              year: e.carYearString ?? e.selectedCarYear?.id?.toString(),
              carSize:
                  e.selectedCarModel?.sizes?.id ??
                  switch (e.carType?.value) {
                    AppStrings.carHauler || null => 1,
                    AppStrings.flatBed => 1.5,
                    _ => 2,
                  },
              isCarPickedUp: e.isCarShouldBePickedUpFromMyLocation,
              pickUpServiceAndDropOffService: e.costId?.value,
              isVerification: e.selectedBrand == null,
              fromCarToBePickedUpLocation: pickUpAddress.value,
              totalDistanceFromCustomerLocationToStopLocation: distance,
              isWinchRequired: e.isWinchRequired,
              carDescription: e.carIssueString,
            ),
          )
          .toList(),
    );
  }

  Future<ExclusiveReqModel> _getUpdatedExclusiveDataModel({
    required bool isExclusive,
    int? oldId,
  }) async {
    final deviceId = await AppCommonFunctions.getDeviceId();
    return ExclusiveReqModel(
      userStartLocation: isExclusive ? originLocationController.value : null,
      userEndLocation: isExclusive ? dropLocationController.value : null,
      customerStartDate: pickupDate.value!.isToday()
          ? DateTime.now()
          : pickupDate.value!,
      customerEndDate: deliveryDate.value!.isToday()
          ? DateTime.now()
          : deliveryDate.value!,
      oldId: oldId,
      allVehicleInOneTrip: isInAllVehicle.value,
      deviceId: deviceId,
      bookingType: isExclusive
          ? BookingType.EXCLUSIVE.name
          : BookingType.SHARED.name,
      endStopLocation: isExclusive
          ? null
          : selectedDropStockLocation.value?.id?.toString(),
      startStopLocation: isExclusive
          ? null
          : selectedOriginStockLocation.value?.id?.toString(),
      carDetails: selectedVehicleInfo
          .map(
            (e) => CarDetailModel(
              serialNumber: e.carSerialString,
              car: e.selectedCarModel?.sizes?.id,
              carName: e.carModelString ?? e.selectedCarModel?.model,
              brand: e.carBrandString ?? e.selectedBrand?.id?.toString(),
              year: e.carYearString ?? e.selectedCarYear?.id?.toString(),
              carSize:
                  e.selectedCarModel?.sizes?.id ??
                  switch (e.carType?.value) {
                    AppStrings.carHauler || null => 1,
                    AppStrings.flatBed => 1.5,
                    _ => 2,
                  },
              isCarPickedUp: e.isCarShouldBePickedUpFromMyLocation,
              pickUpServiceAndDropOffService: e.costId?.value,
              isVerification: e.selectedBrand == null,
              fromCarToBePickedUpLocation: pickUpAddress.value,
              totalDistanceFromCustomerLocationToStopLocation: distance,
              isWinchRequired: e.isWinchRequired,
              carDescription: e.carIssueString,
            ),
          )
          .toList(),
    );
  }

  /// this function called when we press back when api have been called
  void cancelApiToken(CancelToken? cancelToken) {
    if (_isClosed) return;
    try {
      cancelToken?.cancel();
      isShowLoader.value = false;
    } catch (e) {
      'cancelApiToken error: $e'.logE;
    }
  }

  /// for creating vehicle api
  CancelToken? getInsuranceToken;
  Future<void> getInsurance(
    BuildContext context, {
    bool isExclusive = false,
    bool isBack = false,
  }) async {
    if (_isClosed) return;
    getInsuranceToken?.cancel();
    getInsuranceToken = CancelToken();
    try {
      isShowLoader.value = true;
      final response = await homeRepository.createCarVerification(
        ApiRequest(
          path: EndPoints.createCarVerification,
          cancelToken: getInsuranceToken,
        ),
      );
      isShowLoader.value = false;
      response.when(
        success: (data) {
          if (_isClosed || (getInsuranceToken?.isCancelled ?? true)) return;
          context.l10n.bookingDataVerification.showSuccessAlert();
          if (isExclusive && isBack) AppNavigationService.pop(context);
          notify();
        },
        error: (error) {
          if (_isClosed || (getInsuranceToken?.isCancelled ?? true)) return;
          error.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (getInsuranceToken?.isCancelled ?? true)) return;
      isShowLoader.value = false;
      'getInsurance error: $e'.logE;
    }
  }

  /// assign trip data from rested trip
  final isAssignDataLoad = ValueNotifier<bool>(false);
  Future<void> assignData(TripModel data, {required bool isExclusive}) async {
    if (_isClosed) return;
    try {
      isAssignDataLoad.value = true;
      if (data.carDetails?.isNotEmpty ?? false) {
        selectedVehicleInfo.clear();
      }

      oldId.value = data.id;

      isInAllVehicle.value = data.allVehicleInOneTrip ?? false;

      /// assign user select their own start and end location
      originLocationController.value = data.userStartLocation;
      dropLocationController.value = data.userEndLocation;

      /// assign user select their own start and end location fro exclusive
      exclusivePickLocation.value = data.userStartLocation;
      exclusiveDropLocation.value = data.userEndLocation;

      /// assign user selected start and end stock location
      if (data.startStopLocation != null) {
        selectedOriginStockLocation.value = StopLocationData(
          id: data.startStopLocation?.id,
          name: data.startStopLocation?.name,
          address: data.startStopLocation?.address,
        );
        originStockLocationList.value.add(selectedOriginStockLocation.value!);
      }
      if (data.endStopLocation != null) {
        selectedDropStockLocation.value = StopLocationData(
          id: data.endStopLocation?.id,
          name: data.endStopLocation?.name,
          address: data.endStopLocation?.address,
        );
        dropStockLocationList.value.add(selectedDropStockLocation.value!);
      }

      if (_isClosed) return;

      /// assign user selected vehicle
      for (final vehicle in data.carDetails ?? <VehicleInfoModel>[]) {
        if (_isClosed) break;
        if (vehicle.fromCarToBePickedUpLocation != null) {
          pickUpAddress.value = vehicle.fromCarToBePickedUpLocation;
        }
        if (vehicle.verificationStatus != CarVerificationStatus.REJECTED.name) {
          if (_isClosed) break;
          CarCreationModel? selectedBrand;
          CarYearModel? selectedYear;
          for (final brand in carBrands) {
            if (_isClosed) break;
            final yearIndex = brand.years?.indexWhere(
              (e) =>
                  e.models?.any(
                    (m) =>
                        m.sizes?.id == vehicle.car?.id &&
                        brand.carBrand == vehicle.car?.brand,
                  ) ??
                  false,
            );
            if (yearIndex != -1 && yearIndex != null) {
              if (_isClosed) break;
              selectedBrand = brand;
              selectedYear = brand.years?[yearIndex];
              break;
            }
          }
          if (_isClosed) break;
          selectedVehicleInfo.add(
            SelectedVehicleInfoModel(
              carBrandString: vehicle.brand ?? vehicle.car?.brand,
              carModelString: vehicle.model ?? vehicle.car?.model,
              selectedBrand: selectedBrand,
              carModels: selectedYear?.models ?? const [],
              carYearList: selectedBrand?.years ?? const [],
              carType: ValueNotifier(switch (vehicle.size) {
                1 => AppStrings.carHauler,
                1.5 => AppStrings.flatBed,
                2 => AppStrings.towTruck,
                _ => null,
              }),
              selectedCarYear: selectedYear,
              showManualInputs: selectedBrand == null,
              selectedCarModel: selectedYear?.models
                  ?.where((element) => element.sizes?.id == vehicle.car?.id)
                  .firstOrNull,
              carYearString: vehicle.year?.toString(),
              carSerialString: vehicle.serialNumber,
              isWinchRequired: vehicle.isWinchRequired,
              carIssueString: vehicle.carDescription,
              isCarShouldBePickedUpFromMyLocation:
                  vehicle.isCarPickedUpToStopLocation,
              costId:
                  vehicle.pickUpServiceAndDropOffService.isNotEmptyAndNotNull
                  ? ValueNotifier(
                      int.tryParse(vehicle.pickUpServiceAndDropOffService!),
                    )
                  : null,
            ),
          );
        }

        /// add pickup and delivery date
      }
      if (_isClosed) return;
      if (data.carDetails?.any(
            (element) => element.isCarPickedUpToStopLocation,
          ) ??
          false) {
        await getCostOfPickUp(isClear: false);
      }

      /// add pickup and delivery date
      pickupDate.value = data.customerStartDate?.toLocal();
      deliveryDate.value = data.customerEndDate?.toLocal();
      isAssignDataLoad.value = false;
      notify();
    } catch (e) {
      if (_isClosed) return;
      '==>> here error occurred in assigning data $e'.logE;
      isAssignDataLoad.value = false;
    }
  }

  /// clear whole data
  void clearData() {
    try {
      getInsuranceToken?.cancel();
      originStockLocationList.value.clear();
      dropStockLocationList.value.clear();
      originLocationController.value = null;
      dropLocationController.value = null;
      selectedOriginStockLocation.value = null;
      selectedDropStockLocation.value = null;
      exclusivePickLocation.value = null;
      exclusiveDropLocation.value = null;
      Future.delayed(Duration.zero, () {
        selectedVehicleInfo
          ..clear()
          ..add(SelectedVehicleInfoModel());
        notify();
      });
      pickUpAddress.value = null;
      isPickUpSelected = false;
      isInAllVehicle.value = false;
      oldId.value = null;
      pickupDate.value = null;
      deliveryDate.value = null;
      notify();
    } catch (e) {
      'clearData error: $e'.logE;
    }
  }

  /// for creating vehicle api
  CancelToken? updateRestedTripToken;
  Future<void> updateRestedTrip(
    BuildContext context, {
    required String bookingId,
    required bool isExclusive,
  }) async {
    if (_isClosed) return;
    isAssignDataLoad.value = true;
    updateRestedTripToken?.cancel();
    updateRestedTripToken = CancelToken();
    exclusiveReqModel = await _getUpdatedExclusiveDataModel(
      isExclusive: isExclusive,
    );
    if (_isClosed) return;
    try {
      final response = await homeRepository.updateRestedTrip(
        ApiRequest(
          path: EndPoints.updateRestedTrip(bookingId),
          data: exclusiveReqModel?.toJson(),
          cancelToken: updateRestedTripToken,
        ),
      );
      response.when(
        success: (data) {
          if (_isClosed || (updateRestedTripToken?.isCancelled ?? true)) return;
          isAssignDataLoad.value = false;
          context.l10n.bookingDataVerification.showSuccessAlert();
          AppNavigationService.pop(context);
        },
        error: (error) {
          if (_isClosed || (updateRestedTripToken?.isCancelled ?? true)) return;
          isAssignDataLoad.value = false;
          error.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (updateRestedTripToken?.isCancelled ?? true)) return;
      isAssignDataLoad.value = false;
      'updateRestedTrip error: $e'.logE;
    }
  }

  /// below function is use for calculate distance
  /// between origin stop location and user pickup location
  CancelToken? calculateDistanceToken;
  double distance = 0;

  Future<bool> calculateDistance({
    required String lat,
    required String long,
    required BuildContext context,
  }) async {
    if (_isClosed) return false;
    calculateDistanceToken?.cancel();
    calculateDistanceToken = CancelToken();
    final completer = Completer<bool>();
    try {
      isShowLoader.value = true;
      final latLngList = await PlaceApiProvider().getRouteList(
        origin: LatLng(double.tryParse(lat) ?? 0, double.tryParse(long) ?? 0),
        destination: LatLng(
          double.tryParse(
                selectedOriginStockLocation.value?.address?.latitude ?? '0',
              ) ??
              0,
          double.tryParse(
                selectedOriginStockLocation.value?.address?.longitude ?? '0',
              ) ??
              0,
        ),
      );
      if ((calculateDistanceToken?.isCancelled ?? true) || _isClosed) {
        return completer.future;
      }
      if (latLngList.$1.isNotEmpty) {
        distance = latLngList.$2 / 1000;
        completer.complete(true);
      } else {
        completer.complete(false);

        if (context.mounted) {
          context.l10n.pleaseEnterValidPickupAddress.logE;
        }
      }
      if (_isClosed) return false;
      isShowLoader.value = false;
    } catch (e) {
      if ((calculateDistanceToken?.isCancelled ?? true) || _isClosed) {
        return completer.future;
      }
      completer.complete(false);
      'calculateDistance error: $e'.logE;
    }
    return completer.future;
  }

  @override
  void dispose() {
    _isClosed = true;

    // Cancel all tokens
    listNearCancelToken?.cancel();
    getCarCancelToken?.cancel();
    findTransporterToken?.cancel();
    bookExclusiveToken?.cancel();
    createVehicleToken?.cancel();
    getCostOfPickUpToken?.cancel();
    calculateDistanceToken?.cancel();
    updateRestedTripToken?.cancel();
    getInsuranceToken?.cancel();

    // Dispose all controllers
    originLocationController.dispose();
    dropLocationController.dispose();

    // Dispose all notifiers
    isShowLoader.dispose();
    isSearchAddressShowLoader.dispose();
    isAssignDataLoad.dispose();
    originStockLocationList.dispose();
    dropStockLocationList.dispose();
    selectedOriginStockLocation.dispose();
    selectedDropStockLocation.dispose();
    providerList.dispose();
    exclusivePickLocation.dispose();
    exclusiveDropLocation.dispose();
    pickupDate.dispose();
    deliveryDate.dispose();
    isInAllVehicle.dispose();
    winchRequired.dispose();
    oldId.dispose();
    assignCarValue.dispose();
    totalAssignCar.dispose();
    rate.dispose();
    isLowest.dispose();
    is2Days.dispose();
    isWinch.dispose();
    isWinchFix.dispose();
    originController.dispose();
    dropController.dispose();

    super.dispose();
  }
}
