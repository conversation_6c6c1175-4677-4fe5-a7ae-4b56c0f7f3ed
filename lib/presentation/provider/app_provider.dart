import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:transport_match/db/app_db.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/presentation/app_upgrade_page/models/app_upgrade_params.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/logger.dart';

/// This provider is responsible internet connectivity check and other app related general features
class AppProvider extends ChangeNotifier {
  AppProvider() {
    _networkConnectionChecker();
    AppCommonFunctions.getDeviceId().then((value) => deviceId = value);
    // NotificationHelper.initializeNotification();
  }

  // /// FCM token
  // String? fcmToken;

  /// Device Id
  String? deviceId;

  /// Locale
  Locale locale = Locale(Injector.instance<AppDB>().languageCode);

  /// Internet connection status subscription stream
  StreamSubscription<List<ConnectivityResult>>? _streamSubscriptionInternet;

  void _networkConnectionChecker() {
    _streamSubscriptionInternet?.cancel();
    _streamSubscriptionInternet = Connectivity().onConnectivityChanged.listen((
      status,
    ) {
      'onConnectivityChanged : $status'.logD;
      Injector.instance.isReady<AppDB>().then((value) {
        Injector.instance<AppDB>().internetStatus =
            status.contains(ConnectivityResult.mobile) ||
                status.contains(ConnectivityResult.wifi) ||
                status.contains(ConnectivityResult.ethernet) ||
                status.contains(ConnectivityResult.vpn) ||
                status.contains(ConnectivityResult.other)
            ? 'connected'
            : 'disconnected';
      });
    });
  }

  Future<void> checkUpdateAndNavigate() async {
    try {
      final context = rootNavKey.currentContext;
      if (context == null || !context.mounted) {
        'Navigation context is null '.logE;
        return;
      }

      final isConnected =
          Injector.instance<AppDB>().internetStatus == 'connected';
      if (!isConnected) return;
      final packageInfo = await PackageInfo.fromPlatform();
      final currentVersion = packageInfo.version.trim();
      'currentVersion : $currentVersion'.logD;
      const minVersion = '0.9.0';

      if (_isCurrentVersionLower(currentVersion, minVersion)) {
        await AppNavigationService.replaceNamed(
          context,
          AppRoutes.upgradePath,
          extra: AppUpgradeParams(
            currentAppVersion: currentVersion,
            minimumAppVersion: minVersion,
          ),
        );
      } else {
        // await AppNavigationService.replaceNamed(context, AppRoutes.initial);
      }
    } catch (e) {
      'Update check exception: $e'.logE;
    }
  }

  bool _isCurrentVersionLower(String current, String minimum) {
    final currentList = current.split('.').map(int.parse).toList();
    final minList = minimum.split('.').map(int.parse).toList();

    final length = currentList.length > minList.length
        ? currentList.length
        : minList.length;
    currentList.addAll(List.filled(length - currentList.length, 0));
    minList.addAll(List.filled(length - minList.length, 0));

    for (int i = 0; i < length; i++) {
      if (currentList[i] < minList[i]) return true;
      if (currentList[i] > minList[i]) return false;
    }
    return false;
  }

  void changeLocale(Locale locale) {
    this.locale = locale;
    Injector.instance<AppDB>().languageCode = locale.languageCode;
    notifyListeners();
  }

  @override
  void dispose() {
    _streamSubscriptionInternet?.cancel();
    super.dispose();
  }
}
